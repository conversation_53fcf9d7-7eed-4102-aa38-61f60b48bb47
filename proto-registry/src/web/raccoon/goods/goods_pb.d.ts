// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/goods/goods.proto (package step.raccoon.goods, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { FandomWallLayout, GoodsArrangement, GoodsExtra, GoodsProductType, GoodsType, RankFandomWallInfo } from "./common_pb.js";
import type { CensoredState } from "../common/state_pb.js";
import type { Media } from "../common/media_pb.js";
import type { Pagination } from "../common/utils_pb.js";
import type { GameType } from "../common/types_pb.js";

/**
 * @generated from enum step.raccoon.goods.MergeType
 */
export declare enum MergeType {
  /**
   * @generated from enum value: MERGE_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 正片叠底
   *
   * @generated from enum value: MERGE_TYPE_MULTIPLY = 1;
   */
  MULTIPLY = 1,

  /**
   * 叠加
   *
   * @generated from enum value: MERGE_TYPE_OVERLAY = 2;
   */
  OVERLAY = 2,

  /**
   * 正常
   *
   * @generated from enum value: MERGE_TYPE_NORMAL = 3;
   */
  NORMAL = 3,
}

/**
 * @generated from message step.raccoon.goods.AllGoodsCatelogReq
 */
export declare class AllGoodsCatelogReq extends Message<AllGoodsCatelogReq> {
  constructor(data?: PartialMessage<AllGoodsCatelogReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.AllGoodsCatelogReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AllGoodsCatelogReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AllGoodsCatelogReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AllGoodsCatelogReq;

  static equals(a: AllGoodsCatelogReq | PlainMessage<AllGoodsCatelogReq> | undefined, b: AllGoodsCatelogReq | PlainMessage<AllGoodsCatelogReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GoodsProduct
 */
export declare class GoodsProduct extends Message<GoodsProduct> {
  /**
   * 商品id
   *
   * @generated from field: int32 product_id = 1;
   */
  productId: number;

  /**
   * 谷子类型
   *
   * @generated from field: step.raccoon.goods.GoodsType goods_type = 2;
   */
  goodsType: GoodsType;

  /**
   * 商品名称
   *
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * 商品展示图片
   *
   * @generated from field: string image_url = 4;
   */
  imageUrl: string;

  /**
   * 谷子商品类型
   *
   * @generated from field: step.raccoon.goods.GoodsProductType product_type = 5;
   */
  productType: GoodsProductType;

  constructor(data?: PartialMessage<GoodsProduct>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GoodsProduct";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GoodsProduct;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GoodsProduct;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GoodsProduct;

  static equals(a: GoodsProduct | PlainMessage<GoodsProduct> | undefined, b: GoodsProduct | PlainMessage<GoodsProduct> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.AllGoodsCatelogRsp
 */
export declare class AllGoodsCatelogRsp extends Message<AllGoodsCatelogRsp> {
  /**
   * @generated from field: repeated step.raccoon.goods.GoodsProduct products = 1;
   */
  products: GoodsProduct[];

  constructor(data?: PartialMessage<AllGoodsCatelogRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.AllGoodsCatelogRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AllGoodsCatelogRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AllGoodsCatelogRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AllGoodsCatelogRsp;

  static equals(a: AllGoodsCatelogRsp | PlainMessage<AllGoodsCatelogRsp> | undefined, b: AllGoodsCatelogRsp | PlainMessage<AllGoodsCatelogRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.AllCoverEffectsReq
 */
export declare class AllCoverEffectsReq extends Message<AllCoverEffectsReq> {
  /**
   * 谷子类型
   *
   * @generated from field: step.raccoon.goods.GoodsType goods_type = 1;
   */
  goodsType: GoodsType;

  /**
   * 谷子商品类型
   *
   * @generated from field: step.raccoon.goods.GoodsProductType product_type = 2;
   */
  productType: GoodsProductType;

  constructor(data?: PartialMessage<AllCoverEffectsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.AllCoverEffectsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AllCoverEffectsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AllCoverEffectsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AllCoverEffectsReq;

  static equals(a: AllCoverEffectsReq | PlainMessage<AllCoverEffectsReq> | undefined, b: AllCoverEffectsReq | PlainMessage<AllCoverEffectsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.EffectMaterial
 */
export declare class EffectMaterial extends Message<EffectMaterial> {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  /**
   * @generated from field: step.raccoon.goods.MergeType type = 2;
   */
  type: MergeType;

  constructor(data?: PartialMessage<EffectMaterial>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.EffectMaterial";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EffectMaterial;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EffectMaterial;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EffectMaterial;

  static equals(a: EffectMaterial | PlainMessage<EffectMaterial> | undefined, b: EffectMaterial | PlainMessage<EffectMaterial> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.CoverEffect
 */
export declare class CoverEffect extends Message<CoverEffect> {
  /**
   * @generated from field: int32 effect_id = 1;
   */
  effectId: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string image_url = 3;
   */
  imageUrl: string;

  /**
   * @generated from field: repeated step.raccoon.goods.EffectMaterial materials = 4;
   */
  materials: EffectMaterial[];

  constructor(data?: PartialMessage<CoverEffect>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.CoverEffect";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CoverEffect;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CoverEffect;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CoverEffect;

  static equals(a: CoverEffect | PlainMessage<CoverEffect> | undefined, b: CoverEffect | PlainMessage<CoverEffect> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.AllCoverEffectsRsp
 */
export declare class AllCoverEffectsRsp extends Message<AllCoverEffectsRsp> {
  /**
   * @generated from field: repeated step.raccoon.goods.CoverEffect effects = 1;
   */
  effects: CoverEffect[];

  constructor(data?: PartialMessage<AllCoverEffectsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.AllCoverEffectsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AllCoverEffectsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AllCoverEffectsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AllCoverEffectsRsp;

  static equals(a: AllCoverEffectsRsp | PlainMessage<AllCoverEffectsRsp> | undefined, b: AllCoverEffectsRsp | PlainMessage<AllCoverEffectsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.AuditChosenPhotoReq
 */
export declare class AuditChosenPhotoReq extends Message<AuditChosenPhotoReq> {
  /**
   * 图集photoId
   *
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  /**
   * 用户上传图片id
   *
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  constructor(data?: PartialMessage<AuditChosenPhotoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.AuditChosenPhotoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditChosenPhotoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditChosenPhotoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditChosenPhotoReq;

  static equals(a: AuditChosenPhotoReq | PlainMessage<AuditChosenPhotoReq> | undefined, b: AuditChosenPhotoReq | PlainMessage<AuditChosenPhotoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.AuditChosenPhotoRsp
 */
export declare class AuditChosenPhotoRsp extends Message<AuditChosenPhotoRsp> {
  /**
   * 是否审核拒绝
   *
   * @generated from field: bool blocked = 1;
   */
  blocked: boolean;

  constructor(data?: PartialMessage<AuditChosenPhotoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.AuditChosenPhotoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditChosenPhotoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditChosenPhotoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditChosenPhotoRsp;

  static equals(a: AuditChosenPhotoRsp | PlainMessage<AuditChosenPhotoRsp> | undefined, b: AuditChosenPhotoRsp | PlainMessage<AuditChosenPhotoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.CreateGoodsReq
 */
export declare class CreateGoodsReq extends Message<CreateGoodsReq> {
  /**
   * 商品id
   *
   * @generated from field: int32 product_id = 1;
   */
  productId: number;

  /**
   * 商品类型
   *
   * @generated from field: step.raccoon.goods.GoodsProductType product_type = 2;
   */
  productType: GoodsProductType;

  /**
   * 谷子类型
   *
   * @generated from field: step.raccoon.goods.GoodsType goods_type = 3;
   */
  goodsType: GoodsType;

  /**
   * 来源photoId
   *
   * @generated from field: optional string source_photo_id = 4;
   */
  sourcePhotoId?: string;

  /**
   * 覆膜谷子图片Id
   *
   * @generated from field: string display_image_id = 5;
   */
  displayImageId: string;

  /**
   * 原始谷子图片Id
   *
   * @generated from field: string raw_image_id = 6;
   */
  rawImageId: string;

  /**
   * 其余信息，如果是镭射小卡则需要有背景图id
   *
   * @generated from field: step.raccoon.goods.GoodsExtra extra = 7;
   */
  extra?: GoodsExtra;

  /**
   * 来源上传图片id
   *
   * @generated from field: optional string source_image_id = 8;
   */
  sourceImageId?: string;

  constructor(data?: PartialMessage<CreateGoodsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.CreateGoodsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateGoodsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateGoodsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateGoodsReq;

  static equals(a: CreateGoodsReq | PlainMessage<CreateGoodsReq> | undefined, b: CreateGoodsReq | PlainMessage<CreateGoodsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.CreateGoodsRsp
 */
export declare class CreateGoodsRsp extends Message<CreateGoodsRsp> {
  /**
   * 谷子id
   *
   * @generated from field: string goods_id = 1;
   */
  goodsId: string;

  /**
   * 谷子编号
   *
   * @generated from field: string code = 2;
   */
  code: string;

  /**
   * 谷子审核状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored_state = 3;
   */
  censoredState: CensoredState;

  /**
   * 是否是初次创建谷子
   *
   * @generated from field: bool first = 11;
   */
  first: boolean;

  constructor(data?: PartialMessage<CreateGoodsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.CreateGoodsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateGoodsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateGoodsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateGoodsRsp;

  static equals(a: CreateGoodsRsp | PlainMessage<CreateGoodsRsp> | undefined, b: CreateGoodsRsp | PlainMessage<CreateGoodsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.DeleteGoodsReq
 */
export declare class DeleteGoodsReq extends Message<DeleteGoodsReq> {
  /**
   * 谷子id
   *
   * @generated from field: string goods_id = 1;
   */
  goodsId: string;

  constructor(data?: PartialMessage<DeleteGoodsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.DeleteGoodsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteGoodsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteGoodsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteGoodsReq;

  static equals(a: DeleteGoodsReq | PlainMessage<DeleteGoodsReq> | undefined, b: DeleteGoodsReq | PlainMessage<DeleteGoodsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.DeleteGoodsRsp
 */
export declare class DeleteGoodsRsp extends Message<DeleteGoodsRsp> {
  /**
   * 是否有从痛墙上移除
   *
   * @generated from field: bool removed = 11;
   */
  removed: boolean;

  constructor(data?: PartialMessage<DeleteGoodsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.DeleteGoodsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteGoodsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteGoodsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteGoodsRsp;

  static equals(a: DeleteGoodsRsp | PlainMessage<DeleteGoodsRsp> | undefined, b: DeleteGoodsRsp | PlainMessage<DeleteGoodsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetBadgeFramesReq
 */
export declare class GetBadgeFramesReq extends Message<GetBadgeFramesReq> {
  constructor(data?: PartialMessage<GetBadgeFramesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetBadgeFramesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBadgeFramesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBadgeFramesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBadgeFramesReq;

  static equals(a: GetBadgeFramesReq | PlainMessage<GetBadgeFramesReq> | undefined, b: GetBadgeFramesReq | PlainMessage<GetBadgeFramesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetBadgeFramesRsp
 */
export declare class GetBadgeFramesRsp extends Message<GetBadgeFramesRsp> {
  /**
   * @generated from field: repeated step.raccoon.common.Media frames = 1;
   */
  frames: Media[];

  constructor(data?: PartialMessage<GetBadgeFramesRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetBadgeFramesRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBadgeFramesRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBadgeFramesRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBadgeFramesRsp;

  static equals(a: GetBadgeFramesRsp | PlainMessage<GetBadgeFramesRsp> | undefined, b: GetBadgeFramesRsp | PlainMessage<GetBadgeFramesRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenFestivalBadgeReq
 */
export declare class GenFestivalBadgeReq extends Message<GenFestivalBadgeReq> {
  /**
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  constructor(data?: PartialMessage<GenFestivalBadgeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenFestivalBadgeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenFestivalBadgeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenFestivalBadgeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenFestivalBadgeReq;

  static equals(a: GenFestivalBadgeReq | PlainMessage<GenFestivalBadgeReq> | undefined, b: GenFestivalBadgeReq | PlainMessage<GenFestivalBadgeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenFestivalBadgeRsp
 */
export declare class GenFestivalBadgeRsp extends Message<GenFestivalBadgeRsp> {
  /**
   * 新春吧唧图片
   *
   * @generated from field: step.raccoon.common.Media badge_image = 1;
   */
  badgeImage?: Media;

  constructor(data?: PartialMessage<GenFestivalBadgeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenFestivalBadgeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenFestivalBadgeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenFestivalBadgeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenFestivalBadgeRsp;

  static equals(a: GenFestivalBadgeRsp | PlainMessage<GenFestivalBadgeRsp> | undefined, b: GenFestivalBadgeRsp | PlainMessage<GenFestivalBadgeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenLaserTicketReq
 */
export declare class GenLaserTicketReq extends Message<GenLaserTicketReq> {
  /**
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  constructor(data?: PartialMessage<GenLaserTicketReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenLaserTicketReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenLaserTicketReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenLaserTicketReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenLaserTicketReq;

  static equals(a: GenLaserTicketReq | PlainMessage<GenLaserTicketReq> | undefined, b: GenLaserTicketReq | PlainMessage<GenLaserTicketReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenLaserTicketRsp
 */
export declare class GenLaserTicketRsp extends Message<GenLaserTicketRsp> {
  /**
   * @generated from field: step.raccoon.common.Media front_image = 1;
   */
  frontImage?: Media;

  /**
   * @generated from field: step.raccoon.common.Media back_image = 2;
   */
  backImage?: Media;

  constructor(data?: PartialMessage<GenLaserTicketRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenLaserTicketRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenLaserTicketRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenLaserTicketRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenLaserTicketRsp;

  static equals(a: GenLaserTicketRsp | PlainMessage<GenLaserTicketRsp> | undefined, b: GenLaserTicketRsp | PlainMessage<GenLaserTicketRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenBadgeV2Req
 */
export declare class GenBadgeV2Req extends Message<GenBadgeV2Req> {
  /**
   * 图集photoId
   *
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  /**
   * 用户上传图片id
   *
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  /**
   * 用于判断是首次生成还是重新制作（首次生成不用填，重新生成会校验入参是否和上次一致）
   *
   * @generated from field: optional string token = 11;
   */
  token?: string;

  constructor(data?: PartialMessage<GenBadgeV2Req>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenBadgeV2Req";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenBadgeV2Req;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenBadgeV2Req;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenBadgeV2Req;

  static equals(a: GenBadgeV2Req | PlainMessage<GenBadgeV2Req> | undefined, b: GenBadgeV2Req | PlainMessage<GenBadgeV2Req> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenBadgeV2Rsp
 */
export declare class GenBadgeV2Rsp extends Message<GenBadgeV2Rsp> {
  /**
   * 对入参做签名的token，用于重新制作时传入
   *
   * @generated from field: string token = 11;
   */
  token: string;

  constructor(data?: PartialMessage<GenBadgeV2Rsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenBadgeV2Rsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenBadgeV2Rsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenBadgeV2Rsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenBadgeV2Rsp;

  static equals(a: GenBadgeV2Rsp | PlainMessage<GenBadgeV2Rsp> | undefined, b: GenBadgeV2Rsp | PlainMessage<GenBadgeV2Rsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenSquareBadgeV2Req
 */
export declare class GenSquareBadgeV2Req extends Message<GenSquareBadgeV2Req> {
  /**
   * 图集photoId
   *
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  /**
   * 用户上传图片id
   *
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  /**
   * 用于判断是首次生成还是重新制作（首次生成不用填，重新生成会校验入参是否和上次一致）
   *
   * @generated from field: optional string token = 11;
   */
  token?: string;

  constructor(data?: PartialMessage<GenSquareBadgeV2Req>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenSquareBadgeV2Req";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenSquareBadgeV2Req;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenSquareBadgeV2Req;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenSquareBadgeV2Req;

  static equals(a: GenSquareBadgeV2Req | PlainMessage<GenSquareBadgeV2Req> | undefined, b: GenSquareBadgeV2Req | PlainMessage<GenSquareBadgeV2Req> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenSquareBadgeV2Rsp
 */
export declare class GenSquareBadgeV2Rsp extends Message<GenSquareBadgeV2Rsp> {
  /**
   * 对入参做签名的token，用于重新制作时传入
   *
   * @generated from field: string token = 11;
   */
  token: string;

  constructor(data?: PartialMessage<GenSquareBadgeV2Rsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenSquareBadgeV2Rsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenSquareBadgeV2Rsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenSquareBadgeV2Rsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenSquareBadgeV2Rsp;

  static equals(a: GenSquareBadgeV2Rsp | PlainMessage<GenSquareBadgeV2Rsp> | undefined, b: GenSquareBadgeV2Rsp | PlainMessage<GenSquareBadgeV2Rsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenFestivalBadgeV2Req
 */
export declare class GenFestivalBadgeV2Req extends Message<GenFestivalBadgeV2Req> {
  /**
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * 用于判断是首次生成还是重新制作（首次生成不用填，重新生成会校验入参是否和上次一致）
   *
   * @generated from field: optional string token = 11;
   */
  token?: string;

  constructor(data?: PartialMessage<GenFestivalBadgeV2Req>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenFestivalBadgeV2Req";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenFestivalBadgeV2Req;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenFestivalBadgeV2Req;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenFestivalBadgeV2Req;

  static equals(a: GenFestivalBadgeV2Req | PlainMessage<GenFestivalBadgeV2Req> | undefined, b: GenFestivalBadgeV2Req | PlainMessage<GenFestivalBadgeV2Req> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenFestivalBadgeV2Rsp
 */
export declare class GenFestivalBadgeV2Rsp extends Message<GenFestivalBadgeV2Rsp> {
  /**
   * 新春吧唧图片
   *
   * @generated from field: step.raccoon.common.Media badge_image = 1;
   */
  badgeImage?: Media;

  /**
   * 对入参做签名的token，用于重新制作时传入
   *
   * @generated from field: string token = 11;
   */
  token: string;

  constructor(data?: PartialMessage<GenFestivalBadgeV2Rsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenFestivalBadgeV2Rsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenFestivalBadgeV2Rsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenFestivalBadgeV2Rsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenFestivalBadgeV2Rsp;

  static equals(a: GenFestivalBadgeV2Rsp | PlainMessage<GenFestivalBadgeV2Rsp> | undefined, b: GenFestivalBadgeV2Rsp | PlainMessage<GenFestivalBadgeV2Rsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenLaserTicketV2Req
 */
export declare class GenLaserTicketV2Req extends Message<GenLaserTicketV2Req> {
  /**
   * 图集photoId
   *
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  /**
   * 用户上传图片id
   *
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  /**
   * 用于判断是首次生成还是重新制作（首次生成不用填，重新生成会校验入参是否和上次一致）
   *
   * @generated from field: optional string token = 11;
   */
  token?: string;

  constructor(data?: PartialMessage<GenLaserTicketV2Req>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenLaserTicketV2Req";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenLaserTicketV2Req;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenLaserTicketV2Req;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenLaserTicketV2Req;

  static equals(a: GenLaserTicketV2Req | PlainMessage<GenLaserTicketV2Req> | undefined, b: GenLaserTicketV2Req | PlainMessage<GenLaserTicketV2Req> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenLaserTicketV2Rsp
 */
export declare class GenLaserTicketV2Rsp extends Message<GenLaserTicketV2Rsp> {
  /**
   * @generated from field: step.raccoon.common.Media front_image = 1;
   */
  frontImage?: Media;

  /**
   * @generated from field: step.raccoon.common.Media back_image = 2;
   */
  backImage?: Media;

  /**
   * 对入参做签名的token，用于重新制作时传入
   *
   * @generated from field: string token = 11;
   */
  token: string;

  constructor(data?: PartialMessage<GenLaserTicketV2Rsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenLaserTicketV2Rsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenLaserTicketV2Rsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenLaserTicketV2Rsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenLaserTicketV2Rsp;

  static equals(a: GenLaserTicketV2Rsp | PlainMessage<GenLaserTicketV2Rsp> | undefined, b: GenLaserTicketV2Rsp | PlainMessage<GenLaserTicketV2Rsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenStickerV2Req
 */
export declare class GenStickerV2Req extends Message<GenStickerV2Req> {
  /**
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * 用于判断是首次生成还是重新制作（首次生成不用填，重新生成会校验入参是否和上次一致）
   *
   * @generated from field: optional string token = 11;
   */
  token?: string;

  constructor(data?: PartialMessage<GenStickerV2Req>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenStickerV2Req";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenStickerV2Req;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenStickerV2Req;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenStickerV2Req;

  static equals(a: GenStickerV2Req | PlainMessage<GenStickerV2Req> | undefined, b: GenStickerV2Req | PlainMessage<GenStickerV2Req> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenStickerV2Rsp
 */
export declare class GenStickerV2Rsp extends Message<GenStickerV2Rsp> {
  /**
   * 贴纸图片
   *
   * @generated from field: step.raccoon.common.Media sticker_image = 1;
   */
  stickerImage?: Media;

  /**
   * 对入参做签名的token，用于重新制作时传入
   *
   * @generated from field: string token = 11;
   */
  token: string;

  constructor(data?: PartialMessage<GenStickerV2Rsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenStickerV2Rsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenStickerV2Rsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenStickerV2Rsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenStickerV2Rsp;

  static equals(a: GenStickerV2Rsp | PlainMessage<GenStickerV2Rsp> | undefined, b: GenStickerV2Rsp | PlainMessage<GenStickerV2Rsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetPlaceReq
 */
export declare class GetPlaceReq extends Message<GetPlaceReq> {
  /**
   * 被访问空间用户
   *
   * @generated from field: string uid = 1;
   */
  uid: string;

  constructor(data?: PartialMessage<GetPlaceReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetPlaceReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPlaceReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPlaceReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPlaceReq;

  static equals(a: GetPlaceReq | PlainMessage<GetPlaceReq> | undefined, b: GetPlaceReq | PlainMessage<GetPlaceReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetPlaceRsp
 */
export declare class GetPlaceRsp extends Message<GetPlaceRsp> {
  /**
   * 背景墙图片
   *
   * @generated from field: step.raccoon.common.Media background_image = 1;
   */
  backgroundImage?: Media;

  /**
   * 点赞数
   *
   * @generated from field: int32 likes = 2;
   */
  likes: number;

  /**
   * 是否点赞过
   *
   * @generated from field: bool liked = 3;
   */
  liked: boolean;

  /**
   * 访问量
   *
   * @generated from field: int32 visits = 4;
   */
  visits: number;

  constructor(data?: PartialMessage<GetPlaceRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetPlaceRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPlaceRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPlaceRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPlaceRsp;

  static equals(a: GetPlaceRsp | PlainMessage<GetPlaceRsp> | undefined, b: GetPlaceRsp | PlainMessage<GetPlaceRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetFandomWallReq
 */
export declare class GetFandomWallReq extends Message<GetFandomWallReq> {
  /**
   * 被访问痛墙用户
   *
   * @generated from field: string uid = 1;
   */
  uid: string;

  constructor(data?: PartialMessage<GetFandomWallReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetFandomWallReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFandomWallReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFandomWallReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFandomWallReq;

  static equals(a: GetFandomWallReq | PlainMessage<GetFandomWallReq> | undefined, b: GetFandomWallReq | PlainMessage<GetFandomWallReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetFandomWallRsp
 */
export declare class GetFandomWallRsp extends Message<GetFandomWallRsp> {
  /**
   * 痛墙id
   *
   * @generated from field: string wall_id = 1;
   */
  wallId: string;

  /**
   * 痛墙上全部谷子的布局展示信息
   *
   * @generated from field: repeated step.raccoon.goods.GoodsArrangement arrangements = 2;
   */
  arrangements: GoodsArrangement[];

  /**
   * 布局id
   *
   * @generated from field: optional string layout_id = 3;
   */
  layoutId?: string;

  constructor(data?: PartialMessage<GetFandomWallRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetFandomWallRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFandomWallRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFandomWallRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFandomWallRsp;

  static equals(a: GetFandomWallRsp | PlainMessage<GetFandomWallRsp> | undefined, b: GetFandomWallRsp | PlainMessage<GetFandomWallRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.UpdateFandomWallReq
 */
export declare class UpdateFandomWallReq extends Message<UpdateFandomWallReq> {
  /**
   * 痛墙id
   *
   * @generated from field: string wall_id = 1;
   */
  wallId: string;

  /**
   * 痛墙上全部谷子的布局展示信息
   *
   * @generated from field: repeated step.raccoon.goods.GoodsArrangement arrangements = 2;
   */
  arrangements: GoodsArrangement[];

  /**
   * 绘制痛墙图片id
   *
   * @generated from field: string fandom_image_id = 3;
   */
  fandomImageId: string;

  /**
   * 痛墙布局id
   *
   * @generated from field: optional string layout_id = 4;
   */
  layoutId?: string;

  constructor(data?: PartialMessage<UpdateFandomWallReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.UpdateFandomWallReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateFandomWallReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateFandomWallReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateFandomWallReq;

  static equals(a: UpdateFandomWallReq | PlainMessage<UpdateFandomWallReq> | undefined, b: UpdateFandomWallReq | PlainMessage<UpdateFandomWallReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.UpdateFandomWallRsp
 */
export declare class UpdateFandomWallRsp extends Message<UpdateFandomWallRsp> {
  constructor(data?: PartialMessage<UpdateFandomWallRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.UpdateFandomWallRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateFandomWallRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateFandomWallRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateFandomWallRsp;

  static equals(a: UpdateFandomWallRsp | PlainMessage<UpdateFandomWallRsp> | undefined, b: UpdateFandomWallRsp | PlainMessage<UpdateFandomWallRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.LikeFandomWallReq
 */
export declare class LikeFandomWallReq extends Message<LikeFandomWallReq> {
  /**
   * 痛墙id
   *
   * @generated from field: string wall_id = 1;
   */
  wallId: string;

  /**
   * @generated from field: bool like = 2;
   */
  like: boolean;

  constructor(data?: PartialMessage<LikeFandomWallReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.LikeFandomWallReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeFandomWallReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeFandomWallReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeFandomWallReq;

  static equals(a: LikeFandomWallReq | PlainMessage<LikeFandomWallReq> | undefined, b: LikeFandomWallReq | PlainMessage<LikeFandomWallReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.LikeFandomWallRsp
 */
export declare class LikeFandomWallRsp extends Message<LikeFandomWallRsp> {
  constructor(data?: PartialMessage<LikeFandomWallRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.LikeFandomWallRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeFandomWallRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeFandomWallRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeFandomWallRsp;

  static equals(a: LikeFandomWallRsp | PlainMessage<LikeFandomWallRsp> | undefined, b: LikeFandomWallRsp | PlainMessage<LikeFandomWallRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetGoodsListReq
 */
export declare class GetGoodsListReq extends Message<GetGoodsListReq> {
  /**
   * 查询谷子类型，0表示查询全部
   *
   * @generated from field: step.raccoon.goods.GoodsType goods_type = 1;
   */
  goodsType: GoodsType;

  /**
   * 谷子拥有者uid
   *
   * @generated from field: string uid = 2;
   */
  uid: string;

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 20;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<GetGoodsListReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetGoodsListReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGoodsListReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGoodsListReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGoodsListReq;

  static equals(a: GetGoodsListReq | PlainMessage<GetGoodsListReq> | undefined, b: GetGoodsListReq | PlainMessage<GetGoodsListReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GoodsBrief
 */
export declare class GoodsBrief extends Message<GoodsBrief> {
  /**
   * 谷子id
   *
   * @generated from field: string goods_id = 1;
   */
  goodsId: string;

  /**
   * 正面展示图片
   *
   * @generated from field: step.raccoon.common.Media display_image = 2;
   */
  displayImage?: Media;

  /**
   * goods占据痛墙宽高等信息
   *
   * @generated from field: step.raccoon.goods.GoodsExtra extra = 3;
   */
  extra?: GoodsExtra;

  /**
   * 商品id
   *
   * @generated from field: int32 product_id = 4;
   */
  productId: number;

  /**
   * 谷子商品类型
   *
   * @generated from field: step.raccoon.goods.GoodsProductType product_type = 5;
   */
  productType: GoodsProductType;

  /**
   * 谷子审核状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored_state = 6;
   */
  censoredState: CensoredState;

  constructor(data?: PartialMessage<GoodsBrief>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GoodsBrief";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GoodsBrief;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GoodsBrief;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GoodsBrief;

  static equals(a: GoodsBrief | PlainMessage<GoodsBrief> | undefined, b: GoodsBrief | PlainMessage<GoodsBrief> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetGoodsListRsp
 */
export declare class GetGoodsListRsp extends Message<GetGoodsListRsp> {
  /**
   * @generated from field: repeated step.raccoon.goods.GoodsBrief goods = 1;
   */
  goods: GoodsBrief[];

  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 20;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<GetGoodsListRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetGoodsListRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGoodsListRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGoodsListRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGoodsListRsp;

  static equals(a: GetGoodsListRsp | PlainMessage<GetGoodsListRsp> | undefined, b: GetGoodsListRsp | PlainMessage<GetGoodsListRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GoodsDetail
 */
export declare class GoodsDetail extends Message<GoodsDetail> {
  /**
   * 展示图片（第一个是正面图片，其余为其他角度图片，目前仅有背面图片）
   *
   * @generated from field: repeated step.raccoon.common.Media display_images = 1;
   */
  displayImages: Media[];

  /**
   * 原始图片不带覆膜效果（第一个是正面图片，其余为其他角度图片，目前仅有背面图片）
   *
   * @generated from field: repeated step.raccoon.common.Media raw_images = 2;
   */
  rawImages: Media[];

  /**
   * 唯一编号
   *
   * @generated from field: string code = 3;
   */
  code: string;

  /**
   * 谷子类型
   *
   * @generated from field: step.raccoon.goods.GoodsType goods_type = 4;
   */
  goodsType: GoodsType;

  /**
   * 商品id
   *
   * @generated from field: int32 product_id = 5;
   */
  productId: number;

  /**
   * 谷子商品类型
   *
   * @generated from field: step.raccoon.goods.GoodsProductType product_type = 6;
   */
  productType: GoodsProductType;

  /**
   * 谷子审核状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored_state = 7;
   */
  censoredState: CensoredState;

  /**
   * 点赞数
   *
   * @generated from field: int32 likes = 11;
   */
  likes: number;

  /**
   * 是否点赞过
   *
   * @generated from field: bool liked = 12;
   */
  liked: boolean;

  constructor(data?: PartialMessage<GoodsDetail>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GoodsDetail";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GoodsDetail;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GoodsDetail;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GoodsDetail;

  static equals(a: GoodsDetail | PlainMessage<GoodsDetail> | undefined, b: GoodsDetail | PlainMessage<GoodsDetail> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetGoodsDetailReq
 */
export declare class GetGoodsDetailReq extends Message<GetGoodsDetailReq> {
  /**
   * 谷子id
   *
   * @generated from field: string goods_id = 1;
   */
  goodsId: string;

  constructor(data?: PartialMessage<GetGoodsDetailReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetGoodsDetailReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGoodsDetailReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGoodsDetailReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGoodsDetailReq;

  static equals(a: GetGoodsDetailReq | PlainMessage<GetGoodsDetailReq> | undefined, b: GetGoodsDetailReq | PlainMessage<GetGoodsDetailReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetGoodsDetailRsp
 */
export declare class GetGoodsDetailRsp extends Message<GetGoodsDetailRsp> {
  /**
   * @generated from field: step.raccoon.goods.GoodsDetail detail = 1;
   */
  detail?: GoodsDetail;

  constructor(data?: PartialMessage<GetGoodsDetailRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetGoodsDetailRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGoodsDetailRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGoodsDetailRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGoodsDetailRsp;

  static equals(a: GetGoodsDetailRsp | PlainMessage<GetGoodsDetailRsp> | undefined, b: GetGoodsDetailRsp | PlainMessage<GetGoodsDetailRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.LikeGoodsReq
 */
export declare class LikeGoodsReq extends Message<LikeGoodsReq> {
  /**
   * 谷子id
   *
   * @generated from field: string goods_id = 1;
   */
  goodsId: string;

  /**
   * @generated from field: bool like = 2;
   */
  like: boolean;

  constructor(data?: PartialMessage<LikeGoodsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.LikeGoodsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeGoodsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeGoodsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeGoodsReq;

  static equals(a: LikeGoodsReq | PlainMessage<LikeGoodsReq> | undefined, b: LikeGoodsReq | PlainMessage<LikeGoodsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.LikeGoodsRsp
 */
export declare class LikeGoodsRsp extends Message<LikeGoodsRsp> {
  constructor(data?: PartialMessage<LikeGoodsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.LikeGoodsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LikeGoodsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LikeGoodsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LikeGoodsRsp;

  static equals(a: LikeGoodsRsp | PlainMessage<LikeGoodsRsp> | undefined, b: LikeGoodsRsp | PlainMessage<LikeGoodsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetFandomWallLayoutsReq
 */
export declare class GetFandomWallLayoutsReq extends Message<GetFandomWallLayoutsReq> {
  constructor(data?: PartialMessage<GetFandomWallLayoutsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetFandomWallLayoutsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFandomWallLayoutsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFandomWallLayoutsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFandomWallLayoutsReq;

  static equals(a: GetFandomWallLayoutsReq | PlainMessage<GetFandomWallLayoutsReq> | undefined, b: GetFandomWallLayoutsReq | PlainMessage<GetFandomWallLayoutsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetFandomWallLayoutsRsp
 */
export declare class GetFandomWallLayoutsRsp extends Message<GetFandomWallLayoutsRsp> {
  /**
   * @generated from field: repeated step.raccoon.goods.FandomWallLayout layouts = 1;
   */
  layouts: FandomWallLayout[];

  constructor(data?: PartialMessage<GetFandomWallLayoutsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetFandomWallLayoutsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFandomWallLayoutsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFandomWallLayoutsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFandomWallLayoutsRsp;

  static equals(a: GetFandomWallLayoutsRsp | PlainMessage<GetFandomWallLayoutsRsp> | undefined, b: GetFandomWallLayoutsRsp | PlainMessage<GetFandomWallLayoutsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.SaveFandomImageReq
 */
export declare class SaveFandomImageReq extends Message<SaveFandomImageReq> {
  /**
   * @generated from field: string image_id = 1;
   */
  imageId: string;

  /**
   * @generated from field: string wall_id = 2;
   */
  wallId: string;

  constructor(data?: PartialMessage<SaveFandomImageReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.SaveFandomImageReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SaveFandomImageReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SaveFandomImageReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SaveFandomImageReq;

  static equals(a: SaveFandomImageReq | PlainMessage<SaveFandomImageReq> | undefined, b: SaveFandomImageReq | PlainMessage<SaveFandomImageReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.SaveFandomImageRsp
 */
export declare class SaveFandomImageRsp extends Message<SaveFandomImageRsp> {
  /**
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  constructor(data?: PartialMessage<SaveFandomImageRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.SaveFandomImageRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SaveFandomImageRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SaveFandomImageRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SaveFandomImageRsp;

  static equals(a: SaveFandomImageRsp | PlainMessage<SaveFandomImageRsp> | undefined, b: SaveFandomImageRsp | PlainMessage<SaveFandomImageRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenPublishImageReq
 */
export declare class GenPublishImageReq extends Message<GenPublishImageReq> {
  /**
   * 谷子id
   *
   * @generated from field: string goods_id = 1;
   */
  goodsId: string;

  constructor(data?: PartialMessage<GenPublishImageReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenPublishImageReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenPublishImageReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenPublishImageReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenPublishImageReq;

  static equals(a: GenPublishImageReq | PlainMessage<GenPublishImageReq> | undefined, b: GenPublishImageReq | PlainMessage<GenPublishImageReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GenPublishImageRsp
 */
export declare class GenPublishImageRsp extends Message<GenPublishImageRsp> {
  /**
   * 发布图片imageId
   *
   * @generated from field: string image_id = 1;
   */
  imageId: string;

  /**
   * 发布图片url
   *
   * @generated from field: string url = 2;
   */
  url: string;

  /**
   * 发布图片photoId
   *
   * @generated from field: string photo_id = 3;
   */
  photoId: string;

  /**
   * 合图模板id
   *
   * @generated from field: string template_id = 4;
   */
  templateId: string;

  constructor(data?: PartialMessage<GenPublishImageRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GenPublishImageRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenPublishImageRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenPublishImageRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenPublishImageRsp;

  static equals(a: GenPublishImageRsp | PlainMessage<GenPublishImageRsp> | undefined, b: GenPublishImageRsp | PlainMessage<GenPublishImageRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetSourcePhotoReq
 */
export declare class GetSourcePhotoReq extends Message<GetSourcePhotoReq> {
  /**
   * 谷子id 
   *
   * @generated from field: string goods_id = 1;
   */
  goodsId: string;

  constructor(data?: PartialMessage<GetSourcePhotoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetSourcePhotoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSourcePhotoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSourcePhotoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSourcePhotoReq;

  static equals(a: GetSourcePhotoReq | PlainMessage<GetSourcePhotoReq> | undefined, b: GetSourcePhotoReq | PlainMessage<GetSourcePhotoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetSourcePhotoRsp
 */
export declare class GetSourcePhotoRsp extends Message<GetSourcePhotoRsp> {
  /**
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  /**
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  /**
   * @generated from field: string url = 3;
   */
  url: string;

  /**
   * @generated from field: step.raccoon.common.GameType game_type = 4;
   */
  gameType: GameType;

  constructor(data?: PartialMessage<GetSourcePhotoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetSourcePhotoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSourcePhotoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSourcePhotoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSourcePhotoRsp;

  static equals(a: GetSourcePhotoRsp | PlainMessage<GetSourcePhotoRsp> | undefined, b: GetSourcePhotoRsp | PlainMessage<GetSourcePhotoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetUserTopGoodsReq
 */
export declare class GetUserTopGoodsReq extends Message<GetUserTopGoodsReq> {
  /**
   * @generated from field: string uid = 1;
   */
  uid: string;

  constructor(data?: PartialMessage<GetUserTopGoodsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetUserTopGoodsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserTopGoodsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserTopGoodsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserTopGoodsReq;

  static equals(a: GetUserTopGoodsReq | PlainMessage<GetUserTopGoodsReq> | undefined, b: GetUserTopGoodsReq | PlainMessage<GetUserTopGoodsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetUserTopGoodsRsp
 */
export declare class GetUserTopGoodsRsp extends Message<GetUserTopGoodsRsp> {
  /**
   * @generated from field: repeated step.raccoon.goods.GoodsBrief goods = 1;
   */
  goods: GoodsBrief[];

  /**
   * 点赞数
   *
   * @generated from field: int32 likes = 11;
   */
  likes: number;

  constructor(data?: PartialMessage<GetUserTopGoodsRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetUserTopGoodsRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserTopGoodsRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserTopGoodsRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserTopGoodsRsp;

  static equals(a: GetUserTopGoodsRsp | PlainMessage<GetUserTopGoodsRsp> | undefined, b: GetUserTopGoodsRsp | PlainMessage<GetUserTopGoodsRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.VisitHomepageReq
 */
export declare class VisitHomepageReq extends Message<VisitHomepageReq> {
  /**
   * 访问的用户uid
   *
   * @generated from field: string target_uid = 1;
   */
  targetUid: string;

  constructor(data?: PartialMessage<VisitHomepageReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.VisitHomepageReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VisitHomepageReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VisitHomepageReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VisitHomepageReq;

  static equals(a: VisitHomepageReq | PlainMessage<VisitHomepageReq> | undefined, b: VisitHomepageReq | PlainMessage<VisitHomepageReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.VisitHomepageRsp
 */
export declare class VisitHomepageRsp extends Message<VisitHomepageRsp> {
  /**
   * 访问量（pv）
   *
   * @generated from field: int32 visits = 1;
   */
  visits: number;

  constructor(data?: PartialMessage<VisitHomepageRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.VisitHomepageRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VisitHomepageRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VisitHomepageRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VisitHomepageRsp;

  static equals(a: VisitHomepageRsp | PlainMessage<VisitHomepageRsp> | undefined, b: VisitHomepageRsp | PlainMessage<VisitHomepageRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetFandomWallRankListReq
 */
export declare class GetFandomWallRankListReq extends Message<GetFandomWallRankListReq> {
  /**
   * 分页
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 11;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<GetFandomWallRankListReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetFandomWallRankListReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFandomWallRankListReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFandomWallRankListReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFandomWallRankListReq;

  static equals(a: GetFandomWallRankListReq | PlainMessage<GetFandomWallRankListReq> | undefined, b: GetFandomWallRankListReq | PlainMessage<GetFandomWallRankListReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetFandomWallRankListRsp
 */
export declare class GetFandomWallRankListRsp extends Message<GetFandomWallRankListRsp> {
  /**
   * @generated from field: repeated step.raccoon.goods.RankFandomWallInfo fandom_walls = 1;
   */
  fandomWalls: RankFandomWallInfo[];

  /**
   * 分页
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 11;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<GetFandomWallRankListRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetFandomWallRankListRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFandomWallRankListRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFandomWallRankListRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFandomWallRankListRsp;

  static equals(a: GetFandomWallRankListRsp | PlainMessage<GetFandomWallRankListRsp> | undefined, b: GetFandomWallRankListRsp | PlainMessage<GetFandomWallRankListRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetUserFandomWallRankDetailReq
 */
export declare class GetUserFandomWallRankDetailReq extends Message<GetUserFandomWallRankDetailReq> {
  constructor(data?: PartialMessage<GetUserFandomWallRankDetailReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetUserFandomWallRankDetailReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserFandomWallRankDetailReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserFandomWallRankDetailReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserFandomWallRankDetailReq;

  static equals(a: GetUserFandomWallRankDetailReq | PlainMessage<GetUserFandomWallRankDetailReq> | undefined, b: GetUserFandomWallRankDetailReq | PlainMessage<GetUserFandomWallRankDetailReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.goods.GetUserFandomWallRankDetailRsp
 */
export declare class GetUserFandomWallRankDetailRsp extends Message<GetUserFandomWallRankDetailRsp> {
  /**
   * 当前用户上榜痛墙信息
   *
   * @generated from field: repeated step.raccoon.goods.RankFandomWallInfo fandom_wall = 2;
   */
  fandomWall: RankFandomWallInfo[];

  constructor(data?: PartialMessage<GetUserFandomWallRankDetailRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.goods.GetUserFandomWallRankDetailRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserFandomWallRankDetailRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserFandomWallRankDetailRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserFandomWallRankDetailRsp;

  static equals(a: GetUserFandomWallRankDetailRsp | PlainMessage<GetUserFandomWallRankDetailRsp> | undefined, b: GetUserFandomWallRankDetailRsp | PlainMessage<GetUserFandomWallRankDetailRsp> | undefined): boolean;
}

