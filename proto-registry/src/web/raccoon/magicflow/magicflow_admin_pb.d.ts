// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/magicflow/magicflow_admin.proto (package step.raccoon.magicflow, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { GameGroup } from "../common/types_pb.js";
import type { TemplateUIConfig, UIConfig } from "./magicflow_common_pb.js";

/**
 * 自定义配置约束信息
 *
 * @generated from message step.raccoon.magicflow.AdminFlowConfigTemplate
 */
export declare class AdminFlowConfigTemplate extends Message<AdminFlowConfigTemplate> {
  /**
   * @generated from field: step.raccoon.common.GameGroup game_group = 1;
   */
  gameGroup: GameGroup;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: string data = 3;
   */
  data: string;

  /**
   * @generated from field: string creator = 4;
   */
  creator: string;

  /**
   * @generated from field: string updater = 5;
   */
  updater: string;

  /**
   * 毫秒
   *
   * @generated from field: string created_at = 6;
   */
  createdAt: string;

  /**
   * 毫秒
   *
   * @generated from field: string updated_at = 7;
   */
  updatedAt: string;

  constructor(data?: PartialMessage<AdminFlowConfigTemplate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminFlowConfigTemplate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminFlowConfigTemplate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminFlowConfigTemplate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminFlowConfigTemplate;

  static equals(a: AdminFlowConfigTemplate | PlainMessage<AdminFlowConfigTemplate> | undefined, b: AdminFlowConfigTemplate | PlainMessage<AdminFlowConfigTemplate> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminGameTemplate
 */
export declare class AdminGameTemplate extends Message<AdminGameTemplate> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 game_type = 2;
   */
  gameType: number;

  /**
   * 模板名称
   *
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * 素材配置
   *
   * @generated from field: repeated step.raccoon.magicflow.TemplateUIConfig ui_configs = 4;
   */
  uiConfigs: TemplateUIConfig[];

  /**
   * flow平台工作流id
   *
   * @generated from field: string workflow_id = 5;
   */
  workflowId: string;

  /**
   * flow平台工作流版本号, 可空
   * 不填则为最新版本（不推荐）
   *
   * @generated from field: string workflow_version = 6;
   */
  workflowVersion: string;

  /**
   * @generated from field: string creator = 7;
   */
  creator: string;

  /**
   * @generated from field: string updater = 8;
   */
  updater: string;

  /**
   * 毫秒
   *
   * @generated from field: string created_at = 9;
   */
  createdAt: string;

  /**
   * 毫秒
   *
   * @generated from field: string updated_at = 10;
   */
  updatedAt: string;

  /**
   * 自定义配置
   *
   * @generated from field: string config = 11;
   */
  config: string;

  /**
   * 预计完成时间单位s
   *
   * @generated from field: int32 estimate_time = 12;
   */
  estimateTime: number;

  constructor(data?: PartialMessage<AdminGameTemplate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminGameTemplate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminGameTemplate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminGameTemplate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminGameTemplate;

  static equals(a: AdminGameTemplate | PlainMessage<AdminGameTemplate> | undefined, b: AdminGameTemplate | PlainMessage<AdminGameTemplate> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AuditConfig
 */
export declare class AuditConfig extends Message<AuditConfig> {
  /**
   * @generated from field: string biz_type = 1;
   */
  bizType: string;

  /**
   * 图片输入 scene
   *
   * @generated from field: string scene_image_in = 2;
   */
  sceneImageIn: string;

  /**
   * 图片输出 scene
   *
   * @generated from field: string scene_image_out = 3;
   */
  sceneImageOut: string;

  /**
   * 文字输出 scene
   *
   * @generated from field: string scene_brief = 4;
   */
  sceneBrief: string;

  /**
   * 帖子标题
   *
   * @generated from field: string scene_post_title = 5;
   */
  scenePostTitle: string;

  /**
   * 帖子正文
   *
   * @generated from field: string scene_post_content = 6;
   */
  scenePostContent: string;

  /**
   * 帖子图片
   *
   * @generated from field: string scene_post_image = 7;
   */
  scenePostImage: string;

  constructor(data?: PartialMessage<AuditConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AuditConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditConfig;

  static equals(a: AuditConfig | PlainMessage<AuditConfig> | undefined, b: AuditConfig | PlainMessage<AuditConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminGame
 */
export declare class AdminGame extends Message<AdminGame> {
  /**
   * 玩法id
   *
   * @generated from field: int32 game_type = 1;
   */
  gameType: number;

  /**
   * 兼容性版本
   *
   * @generated from field: int32 api_level = 2;
   */
  apiLevel: number;

  /**
   * 玩法名
   *
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * 各种视觉素材
   *
   * @generated from field: step.raccoon.magicflow.UIConfig ui_config = 4;
   */
  uiConfig?: UIConfig;

  /**
   * 审核配置
   *
   * @generated from field: step.raccoon.magicflow.AuditConfig audit_config = 5;
   */
  auditConfig?: AuditConfig;

  /**
   * @generated from field: string creator = 6;
   */
  creator: string;

  /**
   * @generated from field: string updater = 7;
   */
  updater: string;

  /**
   * 毫秒
   *
   * @generated from field: string created_at = 8;
   */
  createdAt: string;

  /**
   * 毫秒
   *
   * @generated from field: string updated_at = 9;
   */
  updatedAt: string;

  /**
   * 自定义配置
   *
   * @generated from field: string config = 10;
   */
  config: string;

  /**
   * 玩法分组
   *
   * @generated from field: step.raccoon.common.GameGroup game_group = 11;
   */
  gameGroup: GameGroup;

  constructor(data?: PartialMessage<AdminGame>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminGame";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminGame;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminGame;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminGame;

  static equals(a: AdminGame | PlainMessage<AdminGame> | undefined, b: AdminGame | PlainMessage<AdminGame> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminListGamesRequest
 */
export declare class AdminListGamesRequest extends Message<AdminListGamesRequest> {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string pagination_token = 2;
   */
  paginationToken: string;

  constructor(data?: PartialMessage<AdminListGamesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminListGamesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminListGamesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminListGamesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminListGamesRequest;

  static equals(a: AdminListGamesRequest | PlainMessage<AdminListGamesRequest> | undefined, b: AdminListGamesRequest | PlainMessage<AdminListGamesRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminListGamesResponse
 */
export declare class AdminListGamesResponse extends Message<AdminListGamesResponse> {
  /**
   * @generated from field: string pagination_token = 1;
   */
  paginationToken: string;

  /**
   * @generated from field: repeated step.raccoon.magicflow.AdminGame games = 2;
   */
  games: AdminGame[];

  constructor(data?: PartialMessage<AdminListGamesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminListGamesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminListGamesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminListGamesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminListGamesResponse;

  static equals(a: AdminListGamesResponse | PlainMessage<AdminListGamesResponse> | undefined, b: AdminListGamesResponse | PlainMessage<AdminListGamesResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminCreateOrUpdateGameRequest
 */
export declare class AdminCreateOrUpdateGameRequest extends Message<AdminCreateOrUpdateGameRequest> {
  /**
   * @generated from field: step.raccoon.magicflow.AdminGame game = 1;
   */
  game?: AdminGame;

  constructor(data?: PartialMessage<AdminCreateOrUpdateGameRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminCreateOrUpdateGameRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminCreateOrUpdateGameRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateGameRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateGameRequest;

  static equals(a: AdminCreateOrUpdateGameRequest | PlainMessage<AdminCreateOrUpdateGameRequest> | undefined, b: AdminCreateOrUpdateGameRequest | PlainMessage<AdminCreateOrUpdateGameRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminCreateOrUpdateGameResponse
 */
export declare class AdminCreateOrUpdateGameResponse extends Message<AdminCreateOrUpdateGameResponse> {
  constructor(data?: PartialMessage<AdminCreateOrUpdateGameResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminCreateOrUpdateGameResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminCreateOrUpdateGameResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateGameResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateGameResponse;

  static equals(a: AdminCreateOrUpdateGameResponse | PlainMessage<AdminCreateOrUpdateGameResponse> | undefined, b: AdminCreateOrUpdateGameResponse | PlainMessage<AdminCreateOrUpdateGameResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminListGameTemplatesRequest
 */
export declare class AdminListGameTemplatesRequest extends Message<AdminListGameTemplatesRequest> {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string pagination_token = 2;
   */
  paginationToken: string;

  /**
   * @generated from field: int32 game_type = 3;
   */
  gameType: number;

  constructor(data?: PartialMessage<AdminListGameTemplatesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminListGameTemplatesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminListGameTemplatesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminListGameTemplatesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminListGameTemplatesRequest;

  static equals(a: AdminListGameTemplatesRequest | PlainMessage<AdminListGameTemplatesRequest> | undefined, b: AdminListGameTemplatesRequest | PlainMessage<AdminListGameTemplatesRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminListGameTemplatesResponse
 */
export declare class AdminListGameTemplatesResponse extends Message<AdminListGameTemplatesResponse> {
  /**
   * @generated from field: string pagination_token = 1;
   */
  paginationToken: string;

  /**
   * @generated from field: repeated step.raccoon.magicflow.AdminGameTemplate templates = 2;
   */
  templates: AdminGameTemplate[];

  constructor(data?: PartialMessage<AdminListGameTemplatesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminListGameTemplatesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminListGameTemplatesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminListGameTemplatesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminListGameTemplatesResponse;

  static equals(a: AdminListGameTemplatesResponse | PlainMessage<AdminListGameTemplatesResponse> | undefined, b: AdminListGameTemplatesResponse | PlainMessage<AdminListGameTemplatesResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminCreateOrUpdateGameTemplateRequest
 */
export declare class AdminCreateOrUpdateGameTemplateRequest extends Message<AdminCreateOrUpdateGameTemplateRequest> {
  /**
   * 不带id为新建，否则修改
   *
   * @generated from field: step.raccoon.magicflow.AdminGameTemplate game_template = 1;
   */
  gameTemplate?: AdminGameTemplate;

  constructor(data?: PartialMessage<AdminCreateOrUpdateGameTemplateRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminCreateOrUpdateGameTemplateRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminCreateOrUpdateGameTemplateRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateGameTemplateRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateGameTemplateRequest;

  static equals(a: AdminCreateOrUpdateGameTemplateRequest | PlainMessage<AdminCreateOrUpdateGameTemplateRequest> | undefined, b: AdminCreateOrUpdateGameTemplateRequest | PlainMessage<AdminCreateOrUpdateGameTemplateRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminCreateOrUpdateGameTemplateResponse
 */
export declare class AdminCreateOrUpdateGameTemplateResponse extends Message<AdminCreateOrUpdateGameTemplateResponse> {
  constructor(data?: PartialMessage<AdminCreateOrUpdateGameTemplateResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminCreateOrUpdateGameTemplateResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminCreateOrUpdateGameTemplateResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateGameTemplateResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateGameTemplateResponse;

  static equals(a: AdminCreateOrUpdateGameTemplateResponse | PlainMessage<AdminCreateOrUpdateGameTemplateResponse> | undefined, b: AdminCreateOrUpdateGameTemplateResponse | PlainMessage<AdminCreateOrUpdateGameTemplateResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminDeleteGameTemplateRequest
 */
export declare class AdminDeleteGameTemplateRequest extends Message<AdminDeleteGameTemplateRequest> {
  /**
   * @generated from field: string template_id = 1;
   */
  templateId: string;

  constructor(data?: PartialMessage<AdminDeleteGameTemplateRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminDeleteGameTemplateRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminDeleteGameTemplateRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminDeleteGameTemplateRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminDeleteGameTemplateRequest;

  static equals(a: AdminDeleteGameTemplateRequest | PlainMessage<AdminDeleteGameTemplateRequest> | undefined, b: AdminDeleteGameTemplateRequest | PlainMessage<AdminDeleteGameTemplateRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminDeleteGameTemplateResponse
 */
export declare class AdminDeleteGameTemplateResponse extends Message<AdminDeleteGameTemplateResponse> {
  constructor(data?: PartialMessage<AdminDeleteGameTemplateResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminDeleteGameTemplateResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminDeleteGameTemplateResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminDeleteGameTemplateResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminDeleteGameTemplateResponse;

  static equals(a: AdminDeleteGameTemplateResponse | PlainMessage<AdminDeleteGameTemplateResponse> | undefined, b: AdminDeleteGameTemplateResponse | PlainMessage<AdminDeleteGameTemplateResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminListConfigTemplatesRequest
 */
export declare class AdminListConfigTemplatesRequest extends Message<AdminListConfigTemplatesRequest> {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string pagination_token = 2;
   */
  paginationToken: string;

  constructor(data?: PartialMessage<AdminListConfigTemplatesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminListConfigTemplatesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminListConfigTemplatesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminListConfigTemplatesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminListConfigTemplatesRequest;

  static equals(a: AdminListConfigTemplatesRequest | PlainMessage<AdminListConfigTemplatesRequest> | undefined, b: AdminListConfigTemplatesRequest | PlainMessage<AdminListConfigTemplatesRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminListConfigTemplatesResponse
 */
export declare class AdminListConfigTemplatesResponse extends Message<AdminListConfigTemplatesResponse> {
  /**
   * @generated from field: string pagination_token = 1;
   */
  paginationToken: string;

  /**
   * @generated from field: repeated step.raccoon.magicflow.AdminFlowConfigTemplate templates = 2;
   */
  templates: AdminFlowConfigTemplate[];

  constructor(data?: PartialMessage<AdminListConfigTemplatesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminListConfigTemplatesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminListConfigTemplatesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminListConfigTemplatesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminListConfigTemplatesResponse;

  static equals(a: AdminListConfigTemplatesResponse | PlainMessage<AdminListConfigTemplatesResponse> | undefined, b: AdminListConfigTemplatesResponse | PlainMessage<AdminListConfigTemplatesResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminCreateOrUpdateConfigTemplateRequest
 */
export declare class AdminCreateOrUpdateConfigTemplateRequest extends Message<AdminCreateOrUpdateConfigTemplateRequest> {
  /**
   * @generated from field: step.raccoon.magicflow.AdminFlowConfigTemplate template = 1;
   */
  template?: AdminFlowConfigTemplate;

  constructor(data?: PartialMessage<AdminCreateOrUpdateConfigTemplateRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminCreateOrUpdateConfigTemplateRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminCreateOrUpdateConfigTemplateRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateConfigTemplateRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateConfigTemplateRequest;

  static equals(a: AdminCreateOrUpdateConfigTemplateRequest | PlainMessage<AdminCreateOrUpdateConfigTemplateRequest> | undefined, b: AdminCreateOrUpdateConfigTemplateRequest | PlainMessage<AdminCreateOrUpdateConfigTemplateRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminCreateOrUpdateConfigTemplateResponse
 */
export declare class AdminCreateOrUpdateConfigTemplateResponse extends Message<AdminCreateOrUpdateConfigTemplateResponse> {
  constructor(data?: PartialMessage<AdminCreateOrUpdateConfigTemplateResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminCreateOrUpdateConfigTemplateResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminCreateOrUpdateConfigTemplateResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateConfigTemplateResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminCreateOrUpdateConfigTemplateResponse;

  static equals(a: AdminCreateOrUpdateConfigTemplateResponse | PlainMessage<AdminCreateOrUpdateConfigTemplateResponse> | undefined, b: AdminCreateOrUpdateConfigTemplateResponse | PlainMessage<AdminCreateOrUpdateConfigTemplateResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminDeleteConfigTemplateRequest
 */
export declare class AdminDeleteConfigTemplateRequest extends Message<AdminDeleteConfigTemplateRequest> {
  /**
   * @generated from field: int32 game_group = 1;
   */
  gameGroup: number;

  constructor(data?: PartialMessage<AdminDeleteConfigTemplateRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminDeleteConfigTemplateRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminDeleteConfigTemplateRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminDeleteConfigTemplateRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminDeleteConfigTemplateRequest;

  static equals(a: AdminDeleteConfigTemplateRequest | PlainMessage<AdminDeleteConfigTemplateRequest> | undefined, b: AdminDeleteConfigTemplateRequest | PlainMessage<AdminDeleteConfigTemplateRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminDeleteConfigTemplateResponse
 */
export declare class AdminDeleteConfigTemplateResponse extends Message<AdminDeleteConfigTemplateResponse> {
  constructor(data?: PartialMessage<AdminDeleteConfigTemplateResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminDeleteConfigTemplateResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminDeleteConfigTemplateResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminDeleteConfigTemplateResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminDeleteConfigTemplateResponse;

  static equals(a: AdminDeleteConfigTemplateResponse | PlainMessage<AdminDeleteConfigTemplateResponse> | undefined, b: AdminDeleteConfigTemplateResponse | PlainMessage<AdminDeleteConfigTemplateResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminQueryConfigTemplateRequest
 */
export declare class AdminQueryConfigTemplateRequest extends Message<AdminQueryConfigTemplateRequest> {
  /**
   * @generated from field: int32 game_group = 1;
   */
  gameGroup: number;

  constructor(data?: PartialMessage<AdminQueryConfigTemplateRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminQueryConfigTemplateRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminQueryConfigTemplateRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminQueryConfigTemplateRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminQueryConfigTemplateRequest;

  static equals(a: AdminQueryConfigTemplateRequest | PlainMessage<AdminQueryConfigTemplateRequest> | undefined, b: AdminQueryConfigTemplateRequest | PlainMessage<AdminQueryConfigTemplateRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminQueryConfigTemplateResponse
 */
export declare class AdminQueryConfigTemplateResponse extends Message<AdminQueryConfigTemplateResponse> {
  /**
   * @generated from field: step.raccoon.magicflow.AdminFlowConfigTemplate template = 1;
   */
  template?: AdminFlowConfigTemplate;

  constructor(data?: PartialMessage<AdminQueryConfigTemplateResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminQueryConfigTemplateResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminQueryConfigTemplateResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminQueryConfigTemplateResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminQueryConfigTemplateResponse;

  static equals(a: AdminQueryConfigTemplateResponse | PlainMessage<AdminQueryConfigTemplateResponse> | undefined, b: AdminQueryConfigTemplateResponse | PlainMessage<AdminQueryConfigTemplateResponse> | undefined): boolean;
}

/**
 * 业务自定义配置，端上可获取
 *
 * @generated from message step.raccoon.magicflow.AdminUpdateGameBusinessConfigRequest
 */
export declare class AdminUpdateGameBusinessConfigRequest extends Message<AdminUpdateGameBusinessConfigRequest> {
  /**
   * @generated from field: int32 game_type = 1;
   */
  gameType: number;

  /**
   * @generated from field: string config = 2;
   */
  config: string;

  constructor(data?: PartialMessage<AdminUpdateGameBusinessConfigRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminUpdateGameBusinessConfigRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminUpdateGameBusinessConfigRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminUpdateGameBusinessConfigRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminUpdateGameBusinessConfigRequest;

  static equals(a: AdminUpdateGameBusinessConfigRequest | PlainMessage<AdminUpdateGameBusinessConfigRequest> | undefined, b: AdminUpdateGameBusinessConfigRequest | PlainMessage<AdminUpdateGameBusinessConfigRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminUpdateGameBusinessConfigResponse
 */
export declare class AdminUpdateGameBusinessConfigResponse extends Message<AdminUpdateGameBusinessConfigResponse> {
  constructor(data?: PartialMessage<AdminUpdateGameBusinessConfigResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminUpdateGameBusinessConfigResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminUpdateGameBusinessConfigResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminUpdateGameBusinessConfigResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminUpdateGameBusinessConfigResponse;

  static equals(a: AdminUpdateGameBusinessConfigResponse | PlainMessage<AdminUpdateGameBusinessConfigResponse> | undefined, b: AdminUpdateGameBusinessConfigResponse | PlainMessage<AdminUpdateGameBusinessConfigResponse> | undefined): boolean;
}

/**
 * 业务自定义配置，端上可获取
 *
 * @generated from message step.raccoon.magicflow.AdminUpdateGameTemplateBusinessConfigRequest
 */
export declare class AdminUpdateGameTemplateBusinessConfigRequest extends Message<AdminUpdateGameTemplateBusinessConfigRequest> {
  /**
   * @generated from field: string template_id = 1;
   */
  templateId: string;

  /**
   * @generated from field: string config = 2;
   */
  config: string;

  constructor(data?: PartialMessage<AdminUpdateGameTemplateBusinessConfigRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminUpdateGameTemplateBusinessConfigRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminUpdateGameTemplateBusinessConfigRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminUpdateGameTemplateBusinessConfigRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminUpdateGameTemplateBusinessConfigRequest;

  static equals(a: AdminUpdateGameTemplateBusinessConfigRequest | PlainMessage<AdminUpdateGameTemplateBusinessConfigRequest> | undefined, b: AdminUpdateGameTemplateBusinessConfigRequest | PlainMessage<AdminUpdateGameTemplateBusinessConfigRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.magicflow.AdminUpdateGameTemplateBusinessConfigResponse
 */
export declare class AdminUpdateGameTemplateBusinessConfigResponse extends Message<AdminUpdateGameTemplateBusinessConfigResponse> {
  constructor(data?: PartialMessage<AdminUpdateGameTemplateBusinessConfigResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.magicflow.AdminUpdateGameTemplateBusinessConfigResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminUpdateGameTemplateBusinessConfigResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminUpdateGameTemplateBusinessConfigResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminUpdateGameTemplateBusinessConfigResponse;

  static equals(a: AdminUpdateGameTemplateBusinessConfigResponse | PlainMessage<AdminUpdateGameTemplateBusinessConfigResponse> | undefined, b: AdminUpdateGameTemplateBusinessConfigResponse | PlainMessage<AdminUpdateGameTemplateBusinessConfigResponse> | undefined): boolean;
}

