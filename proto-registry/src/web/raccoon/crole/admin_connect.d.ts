// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/crole/admin.proto (package step.raccoon.crole, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AddHotRoleConfigReq, AddHotRoleConfigRsp, AddPgcRoleReq, AddPgcRoleRsp, AddRoleMarkConfigReq, AddRoleMarkConfigRsp, AddUgcRoleReq, AddUgcRoleRsp, BatchUpdateRoleStateReq, BatchUpdateRoleStateRes, BatchUploadRoleReq, BatchUploadRoleRes, EditHotRoleConfigReq, EditHotRoleConfigRsp, EditPgcRoleReq, EditPgcRoleRsp, EditRoleMarkConfigReq, EditRoleMarkConfigRsp, EditUgcRoleReq, EditUgcRoleRsp, GetHotRoleConfigListReq, GetHotRoleConfigListRsp, GetHotRoleConfigReq, GetHotRoleConfigRsp, GetRoleMarkConfigListReq, GetRoleMarkConfigListRsp, GetRoleMarkConfigReq, GetRoleMarkConfigRsp, GetRoleReq, GetRoleRsp, ListRoles4GameBindReq, ListRoles4GameBindRsp, RefreshRoleCardListReq, RefreshRoleCardListRsp, RefreshRoleCopyCntReq, RefreshRoleCopyCntRsp, UpdateHotRoleEffectStateReq, UpdateHotRoleEffectStateRsp, UpdateRoleMarkConfigStateReq, UpdateRoleMarkConfigStateRsp, UpdateRoleStateReq, UpdateRoleStateRsp, UpsertRoles4GameBindReq, UpsertRoles4GameBindRsp } from "./admin_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.crole.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.crole.Admin",
  readonly methods: {
    /**
     * 热门角色
     *
     * 配置列表
     *
     * @generated from rpc step.raccoon.crole.Admin.GetHotRoleConfigList
     */
    readonly getHotRoleConfigList: {
      readonly name: "GetHotRoleConfigList",
      readonly I: typeof GetHotRoleConfigListReq,
      readonly O: typeof GetHotRoleConfigListRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 预览配置
     *
     * @generated from rpc step.raccoon.crole.Admin.GetHotRoleConfig
     */
    readonly getHotRoleConfig: {
      readonly name: "GetHotRoleConfig",
      readonly I: typeof GetHotRoleConfigReq,
      readonly O: typeof GetHotRoleConfigRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 配置上下线
     *
     * @generated from rpc step.raccoon.crole.Admin.UpdateHotRoleEffectState
     */
    readonly updateHotRoleEffectState: {
      readonly name: "UpdateHotRoleEffectState",
      readonly I: typeof UpdateHotRoleEffectStateReq,
      readonly O: typeof UpdateHotRoleEffectStateRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 新增配置
     *
     * @generated from rpc step.raccoon.crole.Admin.AddHotRoleConfig
     */
    readonly addHotRoleConfig: {
      readonly name: "AddHotRoleConfig",
      readonly I: typeof AddHotRoleConfigReq,
      readonly O: typeof AddHotRoleConfigRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 修改配置
     *
     * @generated from rpc step.raccoon.crole.Admin.EditHotRoleConfig
     */
    readonly editHotRoleConfig: {
      readonly name: "EditHotRoleConfig",
      readonly I: typeof EditHotRoleConfigReq,
      readonly O: typeof EditHotRoleConfigRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 角色管理
     *
     * 统一角色查询，
     *
     * @generated from rpc step.raccoon.crole.Admin.GetRole
     */
    readonly getRole: {
      readonly name: "GetRole",
      readonly I: typeof GetRoleReq,
      readonly O: typeof GetRoleRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 角色状态修改
     *
     * @generated from rpc step.raccoon.crole.Admin.UpdateRoleState
     */
    readonly updateRoleState: {
      readonly name: "UpdateRoleState",
      readonly I: typeof UpdateRoleStateReq,
      readonly O: typeof UpdateRoleStateRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * ugc管理
     *
     * @generated from rpc step.raccoon.crole.Admin.EditUgcRole
     */
    readonly editUgcRole: {
      readonly name: "EditUgcRole",
      readonly I: typeof EditUgcRoleReq,
      readonly O: typeof EditUgcRoleRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.crole.Admin.AddUgcRole
     */
    readonly addUgcRole: {
      readonly name: "AddUgcRole",
      readonly I: typeof AddUgcRoleReq,
      readonly O: typeof AddUgcRoleRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * pgc管理
     *
     * @generated from rpc step.raccoon.crole.Admin.EditPgcRole
     */
    readonly editPgcRole: {
      readonly name: "EditPgcRole",
      readonly I: typeof EditPgcRoleReq,
      readonly O: typeof EditPgcRoleRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.crole.Admin.AddPgcRole
     */
    readonly addPgcRole: {
      readonly name: "AddPgcRole",
      readonly I: typeof AddPgcRoleReq,
      readonly O: typeof AddPgcRoleRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 回刷角色卡片
     *
     * @generated from rpc step.raccoon.crole.Admin.RefreshRoleCardList
     */
    readonly refreshRoleCardList: {
      readonly name: "RefreshRoleCardList",
      readonly I: typeof RefreshRoleCardListReq,
      readonly O: typeof RefreshRoleCardListRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.crole.Admin.RefreshRoleCopyCnt
     */
    readonly refreshRoleCopyCnt: {
      readonly name: "RefreshRoleCopyCnt",
      readonly I: typeof RefreshRoleCopyCntReq,
      readonly O: typeof RefreshRoleCopyCntRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 创建或更新玩法和角色绑定配置
     *
     * @generated from rpc step.raccoon.crole.Admin.UpsertRoles4GameBind
     */
    readonly upsertRoles4GameBind: {
      readonly name: "UpsertRoles4GameBind",
      readonly I: typeof UpsertRoles4GameBindReq,
      readonly O: typeof UpsertRoles4GameBindRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取玩法角色绑定配置，从未人工配置过是，会默认返回
     *
     * @generated from rpc step.raccoon.crole.Admin.ListRoles4GameBind
     */
    readonly listRoles4GameBind: {
      readonly name: "ListRoles4GameBind",
      readonly I: typeof ListRoles4GameBindReq,
      readonly O: typeof ListRoles4GameBindRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批量导入角色
     *
     * @generated from rpc step.raccoon.crole.Admin.BatchUploadRole
     */
    readonly batchUploadRole: {
      readonly name: "BatchUploadRole",
      readonly I: typeof BatchUploadRoleReq,
      readonly O: typeof BatchUploadRoleRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 批量修改角色状态
     *
     * @generated from rpc step.raccoon.crole.Admin.BatchUpdateRoleState
     */
    readonly batchUpdateRoleState: {
      readonly name: "BatchUpdateRoleState",
      readonly I: typeof BatchUpdateRoleStateReq,
      readonly O: typeof BatchUpdateRoleStateRes,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 角色角标配置
     *
     * 配置列表
     *
     * @generated from rpc step.raccoon.crole.Admin.GetRoleMarkConfigList
     */
    readonly getRoleMarkConfigList: {
      readonly name: "GetRoleMarkConfigList",
      readonly I: typeof GetRoleMarkConfigListReq,
      readonly O: typeof GetRoleMarkConfigListRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 预览配置
     *
     * @generated from rpc step.raccoon.crole.Admin.GetRoleMarkConfig
     */
    readonly getRoleMarkConfig: {
      readonly name: "GetRoleMarkConfig",
      readonly I: typeof GetRoleMarkConfigReq,
      readonly O: typeof GetRoleMarkConfigRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 配置上下线
     *
     * @generated from rpc step.raccoon.crole.Admin.UpdateRoleMarkConfigState
     */
    readonly updateRoleMarkConfigState: {
      readonly name: "UpdateRoleMarkConfigState",
      readonly I: typeof UpdateRoleMarkConfigStateReq,
      readonly O: typeof UpdateRoleMarkConfigStateRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 新增配置
     *
     * @generated from rpc step.raccoon.crole.Admin.AddRoleMarkConfig
     */
    readonly addRoleMarkConfig: {
      readonly name: "AddRoleMarkConfig",
      readonly I: typeof AddRoleMarkConfigReq,
      readonly O: typeof AddRoleMarkConfigRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 修改配置
     *
     * @generated from rpc step.raccoon.crole.Admin.EditRoleMarkConfig
     */
    readonly editRoleMarkConfig: {
      readonly name: "EditRoleMarkConfig",
      readonly I: typeof EditRoleMarkConfigReq,
      readonly O: typeof EditRoleMarkConfigRsp,
      readonly kind: MethodKind.Unary,
    },
  }
};

