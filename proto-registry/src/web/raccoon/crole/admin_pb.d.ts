// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/crole/admin.proto (package step.raccoon.crole, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Pagination, QueryOrderItem } from "../common/utils_pb.js";
import type { CensoredState, RoleState } from "../common/state_pb.js";
import type { RoleGender, RoleImageType } from "../common/role_pb.js";
import type { RoleType, UserProfile } from "../common/profile_pb.js";
import type { GameType } from "../common/types_pb.js";

/**
 * @generated from enum step.raccoon.crole.HotRoleEffectState
 */
export declare enum HotRoleEffectState {
  /**
   * @generated from enum value: HOT_ROLE_EFFECT_STATE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: HOT_ROLE_EFFECT_STATE_ONLINE = 1;
   */
  ONLINE = 1,

  /**
   * @generated from enum value: HOT_ROLE_EFFECT_STATE_OFFLINE = 2;
   */
  OFFLINE = 2,
}

/**
 * @generated from enum step.raccoon.crole.RoleBind4GameMode
 */
export declare enum RoleBind4GameMode {
  /**
   * 黑名单机制
   *
   * @generated from enum value: BIND_MODE_BLACKLIST = 0;
   */
  BIND_MODE_BLACKLIST = 0,

  /**
   * 白名单机制
   *
   * @generated from enum value: BIND_MODE_WHITELIST = 1;
   */
  BIND_MODE_WHITELIST = 1,
}

/**
 * @generated from message step.raccoon.crole.GetRoleReq
 */
export declare class GetRoleReq extends Message<GetRoleReq> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  /**
   * @generated from field: step.raccoon.crole.GetRoleQueryParam search = 2;
   */
  search?: GetRoleQueryParam;

  /**
   * @generated from field: repeated step.raccoon.common.QueryOrderItem order = 3;
   */
  order: QueryOrderItem[];

  constructor(data?: PartialMessage<GetRoleReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetRoleReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRoleReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRoleReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRoleReq;

  static equals(a: GetRoleReq | PlainMessage<GetRoleReq> | undefined, b: GetRoleReq | PlainMessage<GetRoleReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetRoleRsp
 */
export declare class GetRoleRsp extends Message<GetRoleRsp> {
  /**
   * @generated from field: int64 total = 1;
   */
  total: bigint;

  /**
   * @generated from field: string next_cursor = 2;
   */
  nextCursor: string;

  /**
   * @generated from field: repeated step.raccoon.crole.AdminRoleInfo card_list = 3;
   */
  cardList: AdminRoleInfo[];

  constructor(data?: PartialMessage<GetRoleRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetRoleRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRoleRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRoleRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRoleRsp;

  static equals(a: GetRoleRsp | PlainMessage<GetRoleRsp> | undefined, b: GetRoleRsp | PlainMessage<GetRoleRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetRoleQueryParam
 */
export declare class GetRoleQueryParam extends Message<GetRoleQueryParam> {
  /**
   * 角色id
   *
   * @generated from field: repeated string role_id = 1;
   */
  roleId: string[];

  /**
   * 角色名
   *
   * @generated from field: repeated string role_name = 2;
   */
  roleName: string[];

  /**
   * 最小被拍数
   *
   * @generated from field: optional int64 min_copy_cnt = 3;
   */
  minCopyCnt?: bigint;

  /**
   * 最大被拍数
   *
   * @generated from field: optional int64 max_copy_cnt = 4;
   */
  maxCopyCnt?: bigint;

  /**
   * yyyy-MM-dd HH:mm:ss
   *
   * @generated from field: optional string start_time = 5;
   */
  startTime?: string;

  /**
   * yyyy-MM-dd HH:mm:ss
   *
   * @generated from field: optional string end_time = 6;
   */
  endTime?: string;

  /**
   * ip名
   *
   * @generated from field: optional string brand_name = 7;
   */
  brandName?: string;

  /**
   * 状态
   *
   * @generated from field: optional step.raccoon.common.RoleState state = 8;
   */
  state?: RoleState;

  /**
   * 角色创建人员
   *
   * @generated from field: optional string user_name = 9;
   */
  userName?: string;

  /**
   * 性别
   *
   * @generated from field: optional step.raccoon.common.RoleGender gender = 10;
   */
  gender?: RoleGender;

  /**
   * 角色类型
   *
   * @generated from field: optional step.raccoon.common.RoleType role_type = 11;
   */
  roleType?: RoleType;

  /**
   * 审核状态
   *
   * @generated from field: optional step.raccoon.common.CensoredState censor_state = 12;
   */
  censorState?: CensoredState;

  constructor(data?: PartialMessage<GetRoleQueryParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetRoleQueryParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRoleQueryParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRoleQueryParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRoleQueryParam;

  static equals(a: GetRoleQueryParam | PlainMessage<GetRoleQueryParam> | undefined, b: GetRoleQueryParam | PlainMessage<GetRoleQueryParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.AdminRoleInfo
 */
export declare class AdminRoleInfo extends Message<AdminRoleInfo> {
  /**
   * 角色类型 
   *
   * @generated from field: step.raccoon.common.RoleType role_type = 1;
   */
  roleType: RoleType;

  /**
   * 角色id
   *
   * @generated from field: string id = 2;
   */
  id: string;

  /**
   * 角色名
   *
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * 头像
   *
   * @generated from field: string material = 4;
   */
  material: string;

  /**
   * 拍同款数量
   *
   * @generated from field: int32 copy_cnt = 5;
   */
  copyCnt: number;

  /**
   * 创建人-关联的端上用户
   *
   * @generated from field: step.raccoon.common.UserProfile creator = 6;
   */
  creator?: UserProfile;

  /**
   * name
   *
   * @generated from field: string brand_name = 7;
   */
  brandName: string;

  /**
   * id
   *
   * @generated from field: string brand_id = 8;
   */
  brandId: string;

  /**
   * 安全状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored = 9;
   */
  censored: CensoredState;

  /**
   * 角色形象类型
   *
   * @generated from field: step.raccoon.common.RoleImageType role_image_type = 10;
   */
  roleImageType: RoleImageType;

  /**
   * 角色状态
   *
   * @generated from field: step.raccoon.common.RoleState role_state = 11;
   */
  roleState: RoleState;

  /**
   * 别名
   *
   * @generated from field: repeated string alias = 12;
   */
  alias: string[];

  /**
   * 角色描述
   *
   * @generated from field: string role_desc = 13;
   */
  roleDesc: string;

  /**
   * 创建时间 yyyy-MM-dd HH:mm:ss
   *
   * @generated from field: string create_at = 14;
   */
  createAt: string;

  /**
   * 创建时间 yyyy-MM-dd HH:mm:ss
   *
   * @generated from field: string update_at = 15;
   */
  updateAt: string;

  /**
   * 最近更新者
   *
   * @generated from field: string update_user = 16;
   */
  updateUser: string;

  /**
   * 性别
   *
   * @generated from field: step.raccoon.common.RoleGender sexy = 17;
   */
  sexy: RoleGender;

  /**
   * 外显的角色名称
   *
   * @generated from field: string display_role_name = 18;
   */
  displayRoleName: string;

  constructor(data?: PartialMessage<AdminRoleInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.AdminRoleInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminRoleInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminRoleInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminRoleInfo;

  static equals(a: AdminRoleInfo | PlainMessage<AdminRoleInfo> | undefined, b: AdminRoleInfo | PlainMessage<AdminRoleInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.PgcRoleEditInfo
 */
export declare class PgcRoleEditInfo extends Message<PgcRoleEditInfo> {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 2;
   */
  roleName: string;

  /**
   * @generated from field: repeated string role_alias = 3;
   */
  roleAlias: string[];

  /**
   * 头像
   *
   * @generated from field: string portrait = 4;
   */
  portrait: string;

  /**
   * 角色所属的ip
   *
   * @generated from field: string brand_id = 5;
   */
  brandId: string;

  /**
   * 角色卡片简介
   *
   * @generated from field: string role_desc = 6;
   */
  roleDesc: string;

  /**
   * 关联的账户
   *
   * @generated from field: string relate_uid = 7;
   */
  relateUid: string;

  /**
   * 性别
   *
   * @generated from field: step.raccoon.common.RoleGender sexy = 8;
   */
  sexy: RoleGender;

  /**
   * 拍同款数量
   *
   * @generated from field: int32 copy_cnt = 9;
   */
  copyCnt: number;

  /**
   * 展现的角色名称
   *
   * @generated from field: string display_role_name = 10;
   */
  displayRoleName: string;

  constructor(data?: PartialMessage<PgcRoleEditInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.PgcRoleEditInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PgcRoleEditInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PgcRoleEditInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PgcRoleEditInfo;

  static equals(a: PgcRoleEditInfo | PlainMessage<PgcRoleEditInfo> | undefined, b: PgcRoleEditInfo | PlainMessage<PgcRoleEditInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.EditPgcRoleReq
 */
export declare class EditPgcRoleReq extends Message<EditPgcRoleReq> {
  /**
   * 原role_id
   *
   * @generated from field: string role_id = 1;
   */
  roleId: string;

  /**
   * 角色信息
   *
   * @generated from field: step.raccoon.crole.PgcRoleEditInfo edit_info = 2;
   */
  editInfo?: PgcRoleEditInfo;

  constructor(data?: PartialMessage<EditPgcRoleReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.EditPgcRoleReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditPgcRoleReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditPgcRoleReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditPgcRoleReq;

  static equals(a: EditPgcRoleReq | PlainMessage<EditPgcRoleReq> | undefined, b: EditPgcRoleReq | PlainMessage<EditPgcRoleReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.EditPgcRoleRsp
 */
export declare class EditPgcRoleRsp extends Message<EditPgcRoleRsp> {
  constructor(data?: PartialMessage<EditPgcRoleRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.EditPgcRoleRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditPgcRoleRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditPgcRoleRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditPgcRoleRsp;

  static equals(a: EditPgcRoleRsp | PlainMessage<EditPgcRoleRsp> | undefined, b: EditPgcRoleRsp | PlainMessage<EditPgcRoleRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.AddPgcRoleReq
 */
export declare class AddPgcRoleReq extends Message<AddPgcRoleReq> {
  /**
   * @generated from field: step.raccoon.crole.PgcRoleEditInfo role_info = 1;
   */
  roleInfo?: PgcRoleEditInfo;

  constructor(data?: PartialMessage<AddPgcRoleReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.AddPgcRoleReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddPgcRoleReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddPgcRoleReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddPgcRoleReq;

  static equals(a: AddPgcRoleReq | PlainMessage<AddPgcRoleReq> | undefined, b: AddPgcRoleReq | PlainMessage<AddPgcRoleReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.AddPgcRoleRsp
 */
export declare class AddPgcRoleRsp extends Message<AddPgcRoleRsp> {
  constructor(data?: PartialMessage<AddPgcRoleRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.AddPgcRoleRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddPgcRoleRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddPgcRoleRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddPgcRoleRsp;

  static equals(a: AddPgcRoleRsp | PlainMessage<AddPgcRoleRsp> | undefined, b: AddPgcRoleRsp | PlainMessage<AddPgcRoleRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.UpdateRoleStateReq
 */
export declare class UpdateRoleStateReq extends Message<UpdateRoleStateReq> {
  /**
   * @generated from field: repeated string role_ids = 1;
   */
  roleIds: string[];

  /**
   * 状态
   *
   * @generated from field: step.raccoon.common.RoleState target_state = 2;
   */
  targetState: RoleState;

  constructor(data?: PartialMessage<UpdateRoleStateReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.UpdateRoleStateReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRoleStateReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRoleStateReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRoleStateReq;

  static equals(a: UpdateRoleStateReq | PlainMessage<UpdateRoleStateReq> | undefined, b: UpdateRoleStateReq | PlainMessage<UpdateRoleStateReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.UpdateRoleStateRsp
 */
export declare class UpdateRoleStateRsp extends Message<UpdateRoleStateRsp> {
  /**
   * @generated from field: repeated string fail_roles = 1;
   */
  failRoles: string[];

  constructor(data?: PartialMessage<UpdateRoleStateRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.UpdateRoleStateRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRoleStateRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRoleStateRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRoleStateRsp;

  static equals(a: UpdateRoleStateRsp | PlainMessage<UpdateRoleStateRsp> | undefined, b: UpdateRoleStateRsp | PlainMessage<UpdateRoleStateRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetHotRoleConfigReq
 */
export declare class GetHotRoleConfigReq extends Message<GetHotRoleConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  constructor(data?: PartialMessage<GetHotRoleConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetHotRoleConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetHotRoleConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetHotRoleConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetHotRoleConfigReq;

  static equals(a: GetHotRoleConfigReq | PlainMessage<GetHotRoleConfigReq> | undefined, b: GetHotRoleConfigReq | PlainMessage<GetHotRoleConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetHotRoleConfigRsp
 */
export declare class GetHotRoleConfigRsp extends Message<GetHotRoleConfigRsp> {
  /**
   * @generated from field: step.raccoon.crole.HotRoleConfig config = 1;
   */
  config?: HotRoleConfig;

  constructor(data?: PartialMessage<GetHotRoleConfigRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetHotRoleConfigRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetHotRoleConfigRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetHotRoleConfigRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetHotRoleConfigRsp;

  static equals(a: GetHotRoleConfigRsp | PlainMessage<GetHotRoleConfigRsp> | undefined, b: GetHotRoleConfigRsp | PlainMessage<GetHotRoleConfigRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.HotRoleConfig
 */
export declare class HotRoleConfig extends Message<HotRoleConfig> {
  /**
   * 配置id
   *
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * 角色列表
   *
   * @generated from field: repeated step.raccoon.crole.AdminRoleInfo role_list = 2;
   */
  roleList: AdminRoleInfo[];

  /**
   * 状态
   *
   * @generated from field: step.raccoon.crole.HotRoleEffectState state = 3;
   */
  state: HotRoleEffectState;

  /**
   * 更新人
   *
   * @generated from field: string update_user = 4;
   */
  updateUser: string;

  /**
   * yyyy-MM-dd HH:mm:ss
   *
   * @generated from field: string create_at = 5;
   */
  createAt: string;

  /**
   * yyyy-MM-dd HH:mm:ss
   *
   * @generated from field: string update_at = 6;
   */
  updateAt: string;

  constructor(data?: PartialMessage<HotRoleConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.HotRoleConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HotRoleConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HotRoleConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HotRoleConfig;

  static equals(a: HotRoleConfig | PlainMessage<HotRoleConfig> | undefined, b: HotRoleConfig | PlainMessage<HotRoleConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetHotRoleConfigListReq
 */
export declare class GetHotRoleConfigListReq extends Message<GetHotRoleConfigListReq> {
  constructor(data?: PartialMessage<GetHotRoleConfigListReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetHotRoleConfigListReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetHotRoleConfigListReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetHotRoleConfigListReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetHotRoleConfigListReq;

  static equals(a: GetHotRoleConfigListReq | PlainMessage<GetHotRoleConfigListReq> | undefined, b: GetHotRoleConfigListReq | PlainMessage<GetHotRoleConfigListReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetHotRoleConfigListRsp
 */
export declare class GetHotRoleConfigListRsp extends Message<GetHotRoleConfigListRsp> {
  /**
   * @generated from field: repeated step.raccoon.crole.HotRoleConfig config_list = 1;
   */
  configList: HotRoleConfig[];

  constructor(data?: PartialMessage<GetHotRoleConfigListRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetHotRoleConfigListRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetHotRoleConfigListRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetHotRoleConfigListRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetHotRoleConfigListRsp;

  static equals(a: GetHotRoleConfigListRsp | PlainMessage<GetHotRoleConfigListRsp> | undefined, b: GetHotRoleConfigListRsp | PlainMessage<GetHotRoleConfigListRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.UpdateHotRoleEffectStateReq
 */
export declare class UpdateHotRoleEffectStateReq extends Message<UpdateHotRoleEffectStateReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: step.raccoon.crole.HotRoleEffectState target_state = 2;
   */
  targetState: HotRoleEffectState;

  constructor(data?: PartialMessage<UpdateHotRoleEffectStateReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.UpdateHotRoleEffectStateReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateHotRoleEffectStateReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateHotRoleEffectStateReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateHotRoleEffectStateReq;

  static equals(a: UpdateHotRoleEffectStateReq | PlainMessage<UpdateHotRoleEffectStateReq> | undefined, b: UpdateHotRoleEffectStateReq | PlainMessage<UpdateHotRoleEffectStateReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.UpdateHotRoleEffectStateRsp
 */
export declare class UpdateHotRoleEffectStateRsp extends Message<UpdateHotRoleEffectStateRsp> {
  constructor(data?: PartialMessage<UpdateHotRoleEffectStateRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.UpdateHotRoleEffectStateRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateHotRoleEffectStateRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateHotRoleEffectStateRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateHotRoleEffectStateRsp;

  static equals(a: UpdateHotRoleEffectStateRsp | PlainMessage<UpdateHotRoleEffectStateRsp> | undefined, b: UpdateHotRoleEffectStateRsp | PlainMessage<UpdateHotRoleEffectStateRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.AddHotRoleConfigReq
 */
export declare class AddHotRoleConfigReq extends Message<AddHotRoleConfigReq> {
  /**
   * 角色id
   *
   * @generated from field: repeated string role_ids = 1;
   */
  roleIds: string[];

  constructor(data?: PartialMessage<AddHotRoleConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.AddHotRoleConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddHotRoleConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddHotRoleConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddHotRoleConfigReq;

  static equals(a: AddHotRoleConfigReq | PlainMessage<AddHotRoleConfigReq> | undefined, b: AddHotRoleConfigReq | PlainMessage<AddHotRoleConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.AddHotRoleConfigRsp
 */
export declare class AddHotRoleConfigRsp extends Message<AddHotRoleConfigRsp> {
  /**
   * @generated from field: step.raccoon.crole.HotRoleConfig config = 1;
   */
  config?: HotRoleConfig;

  constructor(data?: PartialMessage<AddHotRoleConfigRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.AddHotRoleConfigRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddHotRoleConfigRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddHotRoleConfigRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddHotRoleConfigRsp;

  static equals(a: AddHotRoleConfigRsp | PlainMessage<AddHotRoleConfigRsp> | undefined, b: AddHotRoleConfigRsp | PlainMessage<AddHotRoleConfigRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.UgcRoleEditInfo
 */
export declare class UgcRoleEditInfo extends Message<UgcRoleEditInfo> {
  /**
   * @generated from field: int32 copy_cnt = 1;
   */
  copyCnt: number;

  /**
   * @generated from field: string uid = 11;
   */
  uid: string;

  /**
   * @generated from field: int32 brand_id = 12;
   */
  brandId: number;

  /**
   * @generated from field: string role_name = 13;
   */
  roleName: string;

  /**
   * 性别
   *
   * @generated from field: step.raccoon.common.RoleGender gender = 14;
   */
  gender: RoleGender;

  /**
   * @generated from field: string role_desc = 15;
   */
  roleDesc: string;

  /**
   * @generated from field: string alias = 16;
   */
  alias: string;

  /**
   * @generated from field: string prompt = 51;
   */
  prompt: string;

  /**
   * @generated from field: string prompt_model = 52;
   */
  promptModel: string;

  /**
   * 角色形象类型
   *
   * @generated from field: step.raccoon.common.RoleImageType role_image_type = 53;
   */
  roleImageType: RoleImageType;

  /**
   * @generated from field: string image_id = 54;
   */
  imageId: string;

  /**
   * @generated from field: string image_id_model = 55;
   */
  imageIdModel: string;

  /**
   * 状态
   *
   * @generated from field: step.raccoon.common.RoleState state = 101;
   */
  state: RoleState;

  constructor(data?: PartialMessage<UgcRoleEditInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.UgcRoleEditInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UgcRoleEditInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UgcRoleEditInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UgcRoleEditInfo;

  static equals(a: UgcRoleEditInfo | PlainMessage<UgcRoleEditInfo> | undefined, b: UgcRoleEditInfo | PlainMessage<UgcRoleEditInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.EditUgcRoleReq
 */
export declare class EditUgcRoleReq extends Message<EditUgcRoleReq> {
  /**
   * 原role_id
   *
   * @generated from field: string role_id = 1;
   */
  roleId: string;

  /**
   * 角色信息
   *
   * @generated from field: step.raccoon.crole.UgcRoleEditInfo edit_info = 2;
   */
  editInfo?: UgcRoleEditInfo;

  constructor(data?: PartialMessage<EditUgcRoleReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.EditUgcRoleReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditUgcRoleReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditUgcRoleReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditUgcRoleReq;

  static equals(a: EditUgcRoleReq | PlainMessage<EditUgcRoleReq> | undefined, b: EditUgcRoleReq | PlainMessage<EditUgcRoleReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.EditUgcRoleRsp
 */
export declare class EditUgcRoleRsp extends Message<EditUgcRoleRsp> {
  constructor(data?: PartialMessage<EditUgcRoleRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.EditUgcRoleRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditUgcRoleRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditUgcRoleRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditUgcRoleRsp;

  static equals(a: EditUgcRoleRsp | PlainMessage<EditUgcRoleRsp> | undefined, b: EditUgcRoleRsp | PlainMessage<EditUgcRoleRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.AddUgcRoleReq
 */
export declare class AddUgcRoleReq extends Message<AddUgcRoleReq> {
  /**
   * @generated from field: step.raccoon.crole.UgcRoleEditInfo role_info = 1;
   */
  roleInfo?: UgcRoleEditInfo;

  constructor(data?: PartialMessage<AddUgcRoleReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.AddUgcRoleReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddUgcRoleReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddUgcRoleReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddUgcRoleReq;

  static equals(a: AddUgcRoleReq | PlainMessage<AddUgcRoleReq> | undefined, b: AddUgcRoleReq | PlainMessage<AddUgcRoleReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.AddUgcRoleRsp
 */
export declare class AddUgcRoleRsp extends Message<AddUgcRoleRsp> {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;

  constructor(data?: PartialMessage<AddUgcRoleRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.AddUgcRoleRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddUgcRoleRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddUgcRoleRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddUgcRoleRsp;

  static equals(a: AddUgcRoleRsp | PlainMessage<AddUgcRoleRsp> | undefined, b: AddUgcRoleRsp | PlainMessage<AddUgcRoleRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.EditHotRoleConfigReq
 */
export declare class EditHotRoleConfigReq extends Message<EditHotRoleConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * 角色id
   *
   * @generated from field: repeated string role_ids = 2;
   */
  roleIds: string[];

  constructor(data?: PartialMessage<EditHotRoleConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.EditHotRoleConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditHotRoleConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditHotRoleConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditHotRoleConfigReq;

  static equals(a: EditHotRoleConfigReq | PlainMessage<EditHotRoleConfigReq> | undefined, b: EditHotRoleConfigReq | PlainMessage<EditHotRoleConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.EditHotRoleConfigRsp
 */
export declare class EditHotRoleConfigRsp extends Message<EditHotRoleConfigRsp> {
  /**
   * @generated from field: step.raccoon.crole.HotRoleConfig config = 1;
   */
  config?: HotRoleConfig;

  constructor(data?: PartialMessage<EditHotRoleConfigRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.EditHotRoleConfigRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditHotRoleConfigRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditHotRoleConfigRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditHotRoleConfigRsp;

  static equals(a: EditHotRoleConfigRsp | PlainMessage<EditHotRoleConfigRsp> | undefined, b: EditHotRoleConfigRsp | PlainMessage<EditHotRoleConfigRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.RefreshRoleCardListReq
 */
export declare class RefreshRoleCardListReq extends Message<RefreshRoleCardListReq> {
  constructor(data?: PartialMessage<RefreshRoleCardListReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.RefreshRoleCardListReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshRoleCardListReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshRoleCardListReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshRoleCardListReq;

  static equals(a: RefreshRoleCardListReq | PlainMessage<RefreshRoleCardListReq> | undefined, b: RefreshRoleCardListReq | PlainMessage<RefreshRoleCardListReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.RefreshRoleCardListRsp
 */
export declare class RefreshRoleCardListRsp extends Message<RefreshRoleCardListRsp> {
  constructor(data?: PartialMessage<RefreshRoleCardListRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.RefreshRoleCardListRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshRoleCardListRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshRoleCardListRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshRoleCardListRsp;

  static equals(a: RefreshRoleCardListRsp | PlainMessage<RefreshRoleCardListRsp> | undefined, b: RefreshRoleCardListRsp | PlainMessage<RefreshRoleCardListRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.RefreshRoleCopyCntReq
 */
export declare class RefreshRoleCopyCntReq extends Message<RefreshRoleCopyCntReq> {
  /**
   * @generated from field: bool doRefresh = 1;
   */
  doRefresh: boolean;

  constructor(data?: PartialMessage<RefreshRoleCopyCntReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.RefreshRoleCopyCntReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshRoleCopyCntReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshRoleCopyCntReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshRoleCopyCntReq;

  static equals(a: RefreshRoleCopyCntReq | PlainMessage<RefreshRoleCopyCntReq> | undefined, b: RefreshRoleCopyCntReq | PlainMessage<RefreshRoleCopyCntReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.RefreshRoleCopyCntRsp
 */
export declare class RefreshRoleCopyCntRsp extends Message<RefreshRoleCopyCntRsp> {
  constructor(data?: PartialMessage<RefreshRoleCopyCntRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.RefreshRoleCopyCntRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshRoleCopyCntRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshRoleCopyCntRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshRoleCopyCntRsp;

  static equals(a: RefreshRoleCopyCntRsp | PlainMessage<RefreshRoleCopyCntRsp> | undefined, b: RefreshRoleCopyCntRsp | PlainMessage<RefreshRoleCopyCntRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.Roles4GameBind
 */
export declare class Roles4GameBind extends Message<Roles4GameBind> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * 玩法类型
   *
   * @generated from field: step.raccoon.common.GameType game_type = 2;
   */
  gameType: GameType;

  /**
   * 绑定模式
   *
   * @generated from field: step.raccoon.crole.RoleBind4GameMode mode = 3;
   */
  mode: RoleBind4GameMode;

  /**
   * 角色ID名单列表
   *
   * @generated from field: repeated string role_ids = 4;
   */
  roleIds: string[];

  constructor(data?: PartialMessage<Roles4GameBind>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.Roles4GameBind";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Roles4GameBind;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Roles4GameBind;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Roles4GameBind;

  static equals(a: Roles4GameBind | PlainMessage<Roles4GameBind> | undefined, b: Roles4GameBind | PlainMessage<Roles4GameBind> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.UpsertRoles4GameBindReq
 */
export declare class UpsertRoles4GameBindReq extends Message<UpsertRoles4GameBindReq> {
  /**
   * @generated from field: step.raccoon.crole.Roles4GameBind binding = 1;
   */
  binding?: Roles4GameBind;

  constructor(data?: PartialMessage<UpsertRoles4GameBindReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.UpsertRoles4GameBindReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpsertRoles4GameBindReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpsertRoles4GameBindReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpsertRoles4GameBindReq;

  static equals(a: UpsertRoles4GameBindReq | PlainMessage<UpsertRoles4GameBindReq> | undefined, b: UpsertRoles4GameBindReq | PlainMessage<UpsertRoles4GameBindReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.UpsertRoles4GameBindRsp
 */
export declare class UpsertRoles4GameBindRsp extends Message<UpsertRoles4GameBindRsp> {
  constructor(data?: PartialMessage<UpsertRoles4GameBindRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.UpsertRoles4GameBindRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpsertRoles4GameBindRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpsertRoles4GameBindRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpsertRoles4GameBindRsp;

  static equals(a: UpsertRoles4GameBindRsp | PlainMessage<UpsertRoles4GameBindRsp> | undefined, b: UpsertRoles4GameBindRsp | PlainMessage<UpsertRoles4GameBindRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.ListRoles4GameBindReq
 */
export declare class ListRoles4GameBindReq extends Message<ListRoles4GameBindReq> {
  constructor(data?: PartialMessage<ListRoles4GameBindReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.ListRoles4GameBindReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListRoles4GameBindReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListRoles4GameBindReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListRoles4GameBindReq;

  static equals(a: ListRoles4GameBindReq | PlainMessage<ListRoles4GameBindReq> | undefined, b: ListRoles4GameBindReq | PlainMessage<ListRoles4GameBindReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.ListRoles4GameBindRsp
 */
export declare class ListRoles4GameBindRsp extends Message<ListRoles4GameBindRsp> {
  /**
   * @generated from field: repeated step.raccoon.crole.Roles4GameBind binds = 1;
   */
  binds: Roles4GameBind[];

  constructor(data?: PartialMessage<ListRoles4GameBindRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.ListRoles4GameBindRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListRoles4GameBindRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListRoles4GameBindRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListRoles4GameBindRsp;

  static equals(a: ListRoles4GameBindRsp | PlainMessage<ListRoles4GameBindRsp> | undefined, b: ListRoles4GameBindRsp | PlainMessage<ListRoles4GameBindRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.BatchUploadRoleReq
 */
export declare class BatchUploadRoleReq extends Message<BatchUploadRoleReq> {
  /**
   * csv链接
   *
   * @generated from field: string csv = 1;
   */
  csv: string;

  constructor(data?: PartialMessage<BatchUploadRoleReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.BatchUploadRoleReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchUploadRoleReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchUploadRoleReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchUploadRoleReq;

  static equals(a: BatchUploadRoleReq | PlainMessage<BatchUploadRoleReq> | undefined, b: BatchUploadRoleReq | PlainMessage<BatchUploadRoleReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.BatchUploadRoleRes
 */
export declare class BatchUploadRoleRes extends Message<BatchUploadRoleRes> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  /**
   * 失败原因
   *
   * @generated from field: string fail_reason = 2;
   */
  failReason: string;

  constructor(data?: PartialMessage<BatchUploadRoleRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.BatchUploadRoleRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchUploadRoleRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchUploadRoleRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchUploadRoleRes;

  static equals(a: BatchUploadRoleRes | PlainMessage<BatchUploadRoleRes> | undefined, b: BatchUploadRoleRes | PlainMessage<BatchUploadRoleRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.BatchUpdateRoleStateReq
 */
export declare class BatchUpdateRoleStateReq extends Message<BatchUpdateRoleStateReq> {
  /**
   * @generated from field: repeated string role_ids = 1;
   */
  roleIds: string[];

  /**
   * 状态
   *
   * @generated from field: step.raccoon.common.RoleState state = 2;
   */
  state: RoleState;

  constructor(data?: PartialMessage<BatchUpdateRoleStateReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.BatchUpdateRoleStateReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchUpdateRoleStateReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchUpdateRoleStateReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchUpdateRoleStateReq;

  static equals(a: BatchUpdateRoleStateReq | PlainMessage<BatchUpdateRoleStateReq> | undefined, b: BatchUpdateRoleStateReq | PlainMessage<BatchUpdateRoleStateReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.BatchUpdateRoleStateRes
 */
export declare class BatchUpdateRoleStateRes extends Message<BatchUpdateRoleStateRes> {
  constructor(data?: PartialMessage<BatchUpdateRoleStateRes>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.BatchUpdateRoleStateRes";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BatchUpdateRoleStateRes;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BatchUpdateRoleStateRes;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BatchUpdateRoleStateRes;

  static equals(a: BatchUpdateRoleStateRes | PlainMessage<BatchUpdateRoleStateRes> | undefined, b: BatchUpdateRoleStateRes | PlainMessage<BatchUpdateRoleStateRes> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetRoleMarkConfigListReq
 */
export declare class GetRoleMarkConfigListReq extends Message<GetRoleMarkConfigListReq> {
  constructor(data?: PartialMessage<GetRoleMarkConfigListReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetRoleMarkConfigListReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRoleMarkConfigListReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRoleMarkConfigListReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRoleMarkConfigListReq;

  static equals(a: GetRoleMarkConfigListReq | PlainMessage<GetRoleMarkConfigListReq> | undefined, b: GetRoleMarkConfigListReq | PlainMessage<GetRoleMarkConfigListReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetRoleMarkConfigListRsp
 */
export declare class GetRoleMarkConfigListRsp extends Message<GetRoleMarkConfigListRsp> {
  /**
   * @generated from field: repeated step.raccoon.crole.MarkConfig config = 1;
   */
  config: MarkConfig[];

  constructor(data?: PartialMessage<GetRoleMarkConfigListRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetRoleMarkConfigListRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRoleMarkConfigListRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRoleMarkConfigListRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRoleMarkConfigListRsp;

  static equals(a: GetRoleMarkConfigListRsp | PlainMessage<GetRoleMarkConfigListRsp> | undefined, b: GetRoleMarkConfigListRsp | PlainMessage<GetRoleMarkConfigListRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetRoleMarkConfigReq
 */
export declare class GetRoleMarkConfigReq extends Message<GetRoleMarkConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  constructor(data?: PartialMessage<GetRoleMarkConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetRoleMarkConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRoleMarkConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRoleMarkConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRoleMarkConfigReq;

  static equals(a: GetRoleMarkConfigReq | PlainMessage<GetRoleMarkConfigReq> | undefined, b: GetRoleMarkConfigReq | PlainMessage<GetRoleMarkConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.GetRoleMarkConfigRsp
 */
export declare class GetRoleMarkConfigRsp extends Message<GetRoleMarkConfigRsp> {
  /**
   * 角色列表
   *
   * @generated from field: repeated step.raccoon.crole.AdminRoleInfo role_list = 1;
   */
  roleList: AdminRoleInfo[];

  constructor(data?: PartialMessage<GetRoleMarkConfigRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.GetRoleMarkConfigRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRoleMarkConfigRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRoleMarkConfigRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRoleMarkConfigRsp;

  static equals(a: GetRoleMarkConfigRsp | PlainMessage<GetRoleMarkConfigRsp> | undefined, b: GetRoleMarkConfigRsp | PlainMessage<GetRoleMarkConfigRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.UpdateRoleMarkConfigStateReq
 */
export declare class UpdateRoleMarkConfigStateReq extends Message<UpdateRoleMarkConfigStateReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: step.raccoon.crole.HotRoleEffectState target_state = 2;
   */
  targetState: HotRoleEffectState;

  constructor(data?: PartialMessage<UpdateRoleMarkConfigStateReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.UpdateRoleMarkConfigStateReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRoleMarkConfigStateReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRoleMarkConfigStateReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRoleMarkConfigStateReq;

  static equals(a: UpdateRoleMarkConfigStateReq | PlainMessage<UpdateRoleMarkConfigStateReq> | undefined, b: UpdateRoleMarkConfigStateReq | PlainMessage<UpdateRoleMarkConfigStateReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.UpdateRoleMarkConfigStateRsp
 */
export declare class UpdateRoleMarkConfigStateRsp extends Message<UpdateRoleMarkConfigStateRsp> {
  constructor(data?: PartialMessage<UpdateRoleMarkConfigStateRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.UpdateRoleMarkConfigStateRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRoleMarkConfigStateRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRoleMarkConfigStateRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRoleMarkConfigStateRsp;

  static equals(a: UpdateRoleMarkConfigStateRsp | PlainMessage<UpdateRoleMarkConfigStateRsp> | undefined, b: UpdateRoleMarkConfigStateRsp | PlainMessage<UpdateRoleMarkConfigStateRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.AddRoleMarkConfigReq
 */
export declare class AddRoleMarkConfigReq extends Message<AddRoleMarkConfigReq> {
  /**
   * 角色id
   *
   * @generated from field: repeated string role_ids = 1;
   */
  roleIds: string[];

  constructor(data?: PartialMessage<AddRoleMarkConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.AddRoleMarkConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddRoleMarkConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddRoleMarkConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddRoleMarkConfigReq;

  static equals(a: AddRoleMarkConfigReq | PlainMessage<AddRoleMarkConfigReq> | undefined, b: AddRoleMarkConfigReq | PlainMessage<AddRoleMarkConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.AddRoleMarkConfigRsp
 */
export declare class AddRoleMarkConfigRsp extends Message<AddRoleMarkConfigRsp> {
  /**
   * @generated from field: step.raccoon.crole.MarkConfig config = 1;
   */
  config?: MarkConfig;

  constructor(data?: PartialMessage<AddRoleMarkConfigRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.AddRoleMarkConfigRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddRoleMarkConfigRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddRoleMarkConfigRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddRoleMarkConfigRsp;

  static equals(a: AddRoleMarkConfigRsp | PlainMessage<AddRoleMarkConfigRsp> | undefined, b: AddRoleMarkConfigRsp | PlainMessage<AddRoleMarkConfigRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.EditRoleMarkConfigReq
 */
export declare class EditRoleMarkConfigReq extends Message<EditRoleMarkConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * 角色id
   *
   * @generated from field: repeated string role_ids = 2;
   */
  roleIds: string[];

  constructor(data?: PartialMessage<EditRoleMarkConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.EditRoleMarkConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditRoleMarkConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditRoleMarkConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditRoleMarkConfigReq;

  static equals(a: EditRoleMarkConfigReq | PlainMessage<EditRoleMarkConfigReq> | undefined, b: EditRoleMarkConfigReq | PlainMessage<EditRoleMarkConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.EditRoleMarkConfigRsp
 */
export declare class EditRoleMarkConfigRsp extends Message<EditRoleMarkConfigRsp> {
  /**
   * @generated from field: step.raccoon.crole.MarkConfig config = 1;
   */
  config?: MarkConfig;

  constructor(data?: PartialMessage<EditRoleMarkConfigRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.EditRoleMarkConfigRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditRoleMarkConfigRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditRoleMarkConfigRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditRoleMarkConfigRsp;

  static equals(a: EditRoleMarkConfigRsp | PlainMessage<EditRoleMarkConfigRsp> | undefined, b: EditRoleMarkConfigRsp | PlainMessage<EditRoleMarkConfigRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.crole.MarkConfig
 */
export declare class MarkConfig extends Message<MarkConfig> {
  /**
   * 配置id
   *
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * 状态
   *
   * @generated from field: step.raccoon.crole.HotRoleEffectState state = 2;
   */
  state: HotRoleEffectState;

  /**
   * 更新人
   *
   * @generated from field: string update_user = 4;
   */
  updateUser: string;

  /**
   * yyyy-MM-dd HH:mm:ss
   *
   * @generated from field: string create_at = 5;
   */
  createAt: string;

  /**
   * yyyy-MM-dd HH:mm:ss
   *
   * @generated from field: string update_at = 6;
   */
  updateAt: string;

  /**
   * 配置的角色id
   *
   * @generated from field: repeated string role_ids = 7;
   */
  roleIds: string[];

  constructor(data?: PartialMessage<MarkConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.crole.MarkConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MarkConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MarkConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MarkConfig;

  static equals(a: MarkConfig | PlainMessage<MarkConfig> | undefined, b: MarkConfig | PlainMessage<MarkConfig> | undefined): boolean;
}

