// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/imagegen/imagegen.proto (package step.raccoon.imagegen, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GenImageReq, GenImageRsp, GenImageStreamlyRsp, GenImageV2Req, GenImageV3Req, GenSimpleImageReq, IdentifyAestheticsReq, IdentifyAestheticsResp, IdentifyAnimeReq, IdentifyAnimeResp, IdentifyFacesReq, IdentifyFacesResp, IdentifyWatermarksReq, IdentifyWatermarksResp } from "./imagegen_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.imagegen.ImagegenService
 */
export declare const ImagegenService: {
  readonly typeName: "step.raccoon.imagegen.ImagegenService",
  readonly methods: {
    /**
     * 同步生图，适用于先审后发
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.GemImage
     */
    readonly gemImage: {
      readonly name: "GemImage",
      readonly I: typeof GenImageReq,
      readonly O: typeof GenImageRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 流式同步生图，适用于先发后审
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.GenImageStreamly
     */
    readonly genImageStreamly: {
      readonly name: "GenImageStreamly",
      readonly I: typeof GenImageReq,
      readonly O: typeof GenImageStreamlyRsp,
      readonly kind: MethodKind.ServerStreaming,
    },
    /**
     * 同步生图，适用于先审后发
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.GemImageV2
     */
    readonly gemImageV2: {
      readonly name: "GemImageV2",
      readonly I: typeof GenImageV2Req,
      readonly O: typeof GenImageRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 流式同步生图，适用于先发后审
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.GenImageStreamlyV2
     */
    readonly genImageStreamlyV2: {
      readonly name: "GenImageStreamlyV2",
      readonly I: typeof GenImageV2Req,
      readonly O: typeof GenImageStreamlyRsp,
      readonly kind: MethodKind.ServerStreaming,
    },
    /**
     * 同步生图，适用于先审后发
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.GemImageV3
     */
    readonly gemImageV3: {
      readonly name: "GemImageV3",
      readonly I: typeof GenImageV3Req,
      readonly O: typeof GenImageRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 流式同步生图，适用于先发后审
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.GenImageStreamlyV3
     */
    readonly genImageStreamlyV3: {
      readonly name: "GenImageStreamlyV3",
      readonly I: typeof GenImageV3Req,
      readonly O: typeof GenImageStreamlyRsp,
      readonly kind: MethodKind.ServerStreaming,
    },
    /**
     * 同步生图，供外部业务方使用，仅生成单人ip角色
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.GenSimpleImage
     */
    readonly genSimpleImage: {
      readonly name: "GenSimpleImage",
      readonly I: typeof GenSimpleImageReq,
      readonly O: typeof GenImageRsp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 专有小模型 工具接口
     *
     * 判定图片画面类型，目前有真人/动漫
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.IdentifyAnime
     */
    readonly identifyAnime: {
      readonly name: "IdentifyAnime",
      readonly I: typeof IdentifyAnimeReq,
      readonly O: typeof IdentifyAnimeResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 侦测图片中的水印坐标
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.IdentifyWatermarks
     */
    readonly identifyWatermarks: {
      readonly name: "IdentifyWatermarks",
      readonly I: typeof IdentifyWatermarksReq,
      readonly O: typeof IdentifyWatermarksResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 检测图片人脸
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.IdentifyFaces
     */
    readonly identifyFaces: {
      readonly name: "IdentifyFaces",
      readonly I: typeof IdentifyFacesReq,
      readonly O: typeof IdentifyFacesResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 图片美学分打标
     *
     * @generated from rpc step.raccoon.imagegen.ImagegenService.IdentifyAesthetics
     */
    readonly identifyAesthetics: {
      readonly name: "IdentifyAesthetics",
      readonly I: typeof IdentifyAestheticsReq,
      readonly O: typeof IdentifyAestheticsResp,
      readonly kind: MethodKind.Unary,
    },
  }
};

