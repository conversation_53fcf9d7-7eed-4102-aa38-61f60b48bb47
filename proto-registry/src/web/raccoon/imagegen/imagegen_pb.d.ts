// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/imagegen/imagegen.proto (package step.raccoon.imagegen, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Style } from "../common/imagen_pb.js";
import type { GameType } from "../common/types_pb.js";
import type { InternalImage } from "../common/media_pb.js";

/**
 * @generated from enum step.raccoon.imagegen.ImageGenMode
 */
export declare enum ImageGenMode {
  /**
   * 未知模式
   *
   * @generated from enum value: DRAW_UNKONE = 0;
   */
  DRAW_UNKONE = 0,

  /**
   * 单个ip角色生图
   *
   * @generated from enum value: DRAW_SINGLE = 1;
   */
  DRAW_SINGLE = 1,

  /**
   * 多个ip角色生图
   *
   * @generated from enum value: DRAW_MULTI = 2;
   */
  DRAW_MULTI = 2,

  /**
   * 表情包生图
   *
   * @generated from enum value: DRAW_EMOTION = 3;
   */
  DRAW_EMOTION = 3,

  /**
   * 梗图生图
   *
   * @generated from enum value: DRAW_MEME = 4;
   */
  DRAW_MEME = 4,

  /**
   * 梦核生图
   *
   * @generated from enum value: DRAW_DREAMCORE = 5;
   */
  DRAW_DREAMCORE = 5,

  /**
   * 钥匙扣生图
   *
   * @generated from enum value: DRAW_JELLYCAT = 6;
   */
  DRAW_JELLYCAT = 6,

  /**
   * 宅舞cref生图
   *
   * @generated from enum value: DRAW_OTAKUDANCE = 7;
   */
  DRAW_OTAKUDANCE = 7,

  /**
   * 新春谷子（吧唧）生图
   *
   * @generated from enum value: DRAW_FESTIVAL_GOODS = 8;
   */
  DRAW_FESTIVAL_GOODS = 8,

  /**
   * 节日梗图
   *
   * @generated from enum value: DRAW_FESTIVAL_MEME = 9;
   */
  DRAW_FESTIVAL_MEME = 9,

  /**
   * 春节一起跳 个人形象
   *
   * @generated from enum value: DRAW_DANCE_TOGETHER = 10;
   */
  DRAW_DANCE_TOGETHER = 10,

  /**
   * 谷子镭射票正面生图
   *
   * @generated from enum value: DRAW_GOODS_TICKET_FRONT = 11;
   */
  DRAW_GOODS_TICKET_FRONT = 11,

  /**
   * 谷子镭射票背面生图
   *
   * @generated from enum value: DRAW_GOODS_TICKET_BACK = 12;
   */
  DRAW_GOODS_TICKET_BACK = 12,

  /**
   * 谷子镭射票花纹生图
   *
   * @generated from enum value: DRAW_GOODS_TICKET_FIGURE = 13;
   */
  DRAW_GOODS_TICKET_FIGURE = 13,

  /**
   * 表情包模板生图 2*2
   *
   * @generated from enum value: DRAW_EMOTION_TMPL = 14;
   */
  DRAW_EMOTION_TMPL = 14,

  /**
   * 表情包模板生图 3*3
   *
   * @generated from enum value: DRAW_EMOTION_TMPL_NINE = 15;
   */
  DRAW_EMOTION_TMPL_NINE = 15,

  /**
   * ugc动漫角色贴纸生图
   *
   * @generated from enum value: DRAW_GOODS_UGC_CARTOON_STICKER = 16;
   */
  DRAW_GOODS_UGC_CARTOON_STICKER = 16,

  /**
   * ugc真人角色贴纸生图
   *
   * @generated from enum value: DRAW_GOODS_UGC_REAL_STICKER = 17;
   */
  DRAW_GOODS_UGC_REAL_STICKER = 17,

  /**
   * 官方角色贴纸生图
   *
   * @generated from enum value: DRAW_GOODS_OFFICIAL_STICKER = 18;
   */
  DRAW_GOODS_OFFICIAL_STICKER = 18,
}

/**
 * @generated from enum step.raccoon.imagegen.ModelSchedStrategy
 */
export declare enum ModelSchedStrategy {
  /**
   * 根据请求条件，在多个模型可用的场景下，随机选择一个模型生成所有图片
   *
   * @generated from enum value: RANDOM_SELECT = 0;
   */
  RANDOM_SELECT = 0,

  /**
   * 根据请求条件，在多个模型可用的场景下，每张图片随机选择一个模型生成
   *
   * @generated from enum value: ROUND_ROBIN = 1;
   */
  ROUND_ROBIN = 1,
}

/**
 * @generated from enum step.raccoon.imagegen.AnimeType
 */
export declare enum AnimeType {
  /**
   * 未知画面类型
   *
   * @generated from enum value: UNKOWN = 0;
   */
  UNKOWN = 0,

  /**
   * 卡通画面类型
   *
   * @generated from enum value: CARTOON = 1;
   */
  CARTOON = 1,

  /**
   * 真人画面类型
   *
   * @generated from enum value: REALISTIC = 2;
   */
  REALISTIC = 2,
}

/**
 * @generated from message step.raccoon.imagegen.ImageTrace
 */
export declare class ImageTrace extends Message<ImageTrace> {
  /**
   * 生图模型
   *
   * @generated from field: string model = 1;
   */
  model: string;

  /**
   * 生图底模
   *
   * @generated from field: string base_model = 2;
   */
  baseModel: string;

  /**
   * 最终进入sd模型的prompt
   *
   * @generated from field: string prompt = 3;
   */
  prompt: string;

  /**
   * 最终进入sd模型的参数 json string
   *
   * @generated from field: string params = 4;
   */
  params: string;

  /**
   * 模型的prmopt格式化模版
   *
   * @generated from field: string prompt_format = 5;
   */
  promptFormat: string;

  /**
   * sd模型接口耗时
   *
   * @generated from field: int64 model_cost = 6;
   */
  modelCost: bigint;

  /**
   * 图片上传耗时
   *
   * @generated from field: int64 upload_cost = 7;
   */
  uploadCost: bigint;

  /**
   * 模型请求id，主要用于模型问题的查证用
   *
   * @generated from field: string x_request_id = 8;
   */
  xRequestId: string;

  /**
   * lora模型名称
   *
   * @generated from field: string lora = 9;
   */
  lora: string;

  /**
   * 模型迭代的版本号，形式为：0,1,2 ...
   *
   * @generated from field: uint32 model_version = 10;
   */
  modelVersion: number;

  constructor(data?: PartialMessage<ImageTrace>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.ImageTrace";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImageTrace;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImageTrace;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImageTrace;

  static equals(a: ImageTrace | PlainMessage<ImageTrace> | undefined, b: ImageTrace | PlainMessage<ImageTrace> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.Image
 */
export declare class Image extends Message<Image> {
  /**
   * 存储到media service的图片id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 图片url
   *
   * @generated from field: string url = 2;
   */
  url: string;

  /**
   * 图片内部url
   *
   * @generated from field: string in_url = 6;
   */
  inUrl: string;

  /**
   * 生图错误信息，调用方应该先判断此字段是否为“”
   *
   * @generated from field: string error = 3;
   */
  error: string;

  /**
   * 图片json meta string
   *
   * @generated from field: string meta = 4;
   */
  meta: string;

  /**
   * 生图的trace字典
   *
   * @generated from field: step.raccoon.imagegen.ImageTrace trace = 5;
   */
  trace?: ImageTrace;

  constructor(data?: PartialMessage<Image>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.Image";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Image;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Image;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Image;

  static equals(a: Image | PlainMessage<Image> | undefined, b: Image | PlainMessage<Image> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.Role
 */
export declare class Role extends Message<Role> {
  /**
   * ip type
   *
   * @generated from field: int32 brand_type = 1;
   */
  brandType: number;

  /**
   * 角色id
   *
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * 角色触发词（英文），cref生图用(required)，非cref角色一定不要传
   *
   * @generated from field: string prompt = 3;
   */
  prompt: string;

  /**
   * 角色形象图片url，cref生图用(required)，非cref角色一定不要传
   *
   * @generated from field: string image_url = 4;
   */
  imageUrl: string;

  /**
   * 角色额外信息，crole需要传入role_image_type，参见step.raccoon.common.role
   *
   * @generated from field: string extra = 5;
   */
  extra: string;

  constructor(data?: PartialMessage<Role>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.Role";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Role;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Role;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Role;

  static equals(a: Role | PlainMessage<Role> | undefined, b: Role | PlainMessage<Role> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.GenImageReq
 */
export declare class GenImageReq extends Message<GenImageReq> {
  /**
   * 图片风格
   *
   * @generated from field: step.raccoon.common.Style style = 1;
   */
  style: Style;

  /**
   * 玩法
   *
   * @generated from field: step.raccoon.common.GameType game_type = 2;
   */
  gameType: GameType;

  /**
   * @generated from field: step.raccoon.imagegen.ImageGenMode mode = 3;
   */
  mode: ImageGenMode;

  /**
   * ip角色列表
   *
   * @generated from field: repeated step.raccoon.imagegen.Role roles = 4;
   */
  roles: Role[];

  /**
   * json dict string, 指定mode下，额外需要的生图参数。目前只有双人生图模型，可选填双人interact:prompt.....
   *
   * @generated from field: optional string extra = 5;
   */
  extra?: string;

  /**
   *  每个角色专属的生图英文prompt
   *
   * @generated from field: repeated string prompts = 6;
   */
  prompts: string[];

  /**
   * 指定模型，一般不需指定，预留给未来场景
   *
   * @generated from field: optional string model = 7;
   */
  model?: string;

  /**
   * 指定底模，一般不需指定，预留给未来场景
   *
   * @generated from field: optional string base_model = 8;
   */
  baseModel?: string;

  /**
   * @generated from field: int32 width = 9;
   */
  width: number;

  /**
   * @generated from field: int32 height = 10;
   */
  height: number;

  /**
   * 图片数量
   *
   * @generated from field: int32 n = 11;
   */
  n: number;

  /**
   * 是否并行生成，默认为true，后端最多单次3个并行生图
   *
   * @generated from field: optional bool paralle = 12;
   */
  paralle?: boolean;

  /**
   * 路由到多个模型时的模型生度调度策略，默认为ROUND_ROBIN
   *
   * @generated from field: optional step.raccoon.imagegen.ModelSchedStrategy strategy = 13;
   */
  strategy?: ModelSchedStrategy;

  /**
   * 业务侧需要透传的生图模型参数，默认为空，json dict string，请严格根据业务需要透传，具体参考stepcast的生图接口文档
   *
   * @generated from field: string params = 14;
   */
  params: string;

  /**
   * 路由到超分模型时，是否进行超分（true时，超分模型会默认将size进行x2scale
   *
   * @generated from field: bool upscale = 15;
   */
  upscale: boolean;

  /**
   * 生图去除角色效果，只保留角色的性别信息
   *
   * @generated from field: bool remove_character = 16;
   */
  removeCharacter: boolean;

  /**
   * 强制使用动态lora workflow加载方案
   *
   * @generated from field: bool force_dymaic_lora = 17;
   */
  forceDymaicLora: boolean;

  /**
   * 指定需要的场景的模型版本
   *
   * @generated from field: uint32 model_version = 18;
   */
  modelVersion: number;

  constructor(data?: PartialMessage<GenImageReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.GenImageReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenImageReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenImageReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenImageReq;

  static equals(a: GenImageReq | PlainMessage<GenImageReq> | undefined, b: GenImageReq | PlainMessage<GenImageReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.GenImageRsp
 */
export declare class GenImageRsp extends Message<GenImageRsp> {
  /**
   * 生成的图片列表
   *
   * @generated from field: repeated step.raccoon.imagegen.Image images = 1;
   */
  images: Image[];

  constructor(data?: PartialMessage<GenImageRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.GenImageRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenImageRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenImageRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenImageRsp;

  static equals(a: GenImageRsp | PlainMessage<GenImageRsp> | undefined, b: GenImageRsp | PlainMessage<GenImageRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.GenImageStreamlyRsp
 */
export declare class GenImageStreamlyRsp extends Message<GenImageStreamlyRsp> {
  /**
   * 流式增量返回生成的图片列表
   *
   * @generated from field: repeated step.raccoon.imagegen.Image images = 1;
   */
  images: Image[];

  /**
   * 是否结束
   *
   * @generated from field: bool finished = 2;
   */
  finished: boolean;

  /**
   * 生图中断或异常时的error信息
   *
   * @generated from field: string error = 3;
   */
  error: string;

  constructor(data?: PartialMessage<GenImageStreamlyRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.GenImageStreamlyRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenImageStreamlyRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenImageStreamlyRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenImageStreamlyRsp;

  static equals(a: GenImageStreamlyRsp | PlainMessage<GenImageStreamlyRsp> | undefined, b: GenImageStreamlyRsp | PlainMessage<GenImageStreamlyRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.GenImageV2Req
 */
export declare class GenImageV2Req extends Message<GenImageV2Req> {
  /**
   * 图片风格
   *
   * @generated from field: step.raccoon.common.Style style = 1;
   */
  style: Style;

  /**
   * 玩法
   *
   * @generated from field: step.raccoon.common.GameType game_type = 2;
   */
  gameType: GameType;

  /**
   * @generated from field: step.raccoon.imagegen.ImageGenMode mode = 3;
   */
  mode: ImageGenMode;

  /**
   * ip角色列表
   *
   * @generated from field: repeated step.raccoon.imagegen.Role roles = 4;
   */
  roles: Role[];

  /**
   * json dict string, 指定mode下，额外需要的生图参数。目前只有双人生图模型，可选填双人interact:prompt.....
   *
   * @generated from field: string extra = 5;
   */
  extra: string;

  /**
   *  每个角色专属的生图英文prompt
   *
   * @generated from field: repeated string prompts = 6;
   */
  prompts: string[];

  /**
   * @generated from field: int32 width = 7;
   */
  width: number;

  /**
   * @generated from field: int32 height = 8;
   */
  height: number;

  /**
   * 图片数量
   *
   * @generated from field: int32 n = 9;
   */
  n: number;

  /**
   * 是否并行生成，默认为true，后端最多单次3个并行生图
   *
   * @generated from field: bool paralle = 10;
   */
  paralle: boolean;

  /**
   * 路由到多个模型时的模型生度调度策略，默认为ROUND_ROBIN
   *
   * @generated from field: step.raccoon.imagegen.ModelSchedStrategy strategy = 11;
   */
  strategy: ModelSchedStrategy;

  /**
   * 业务侧需要透传的生图模型参数，默认为空，json dict string，请严格根据业务需要透传，具体参考stepcast的生图接口文档
   *
   * @generated from field: string params = 12;
   */
  params: string;

  /**
   * 路由到超分模型时，是否进行超分（true时，超分模型会默认将size进行x2scale
   *
   * @generated from field: bool upscale = 13;
   */
  upscale: boolean;

  /**
   * 生图去除角色效果，只保留角色的性别信息
   *
   * @generated from field: bool remove_character = 14;
   */
  removeCharacter: boolean;

  /**
   * sref生图依赖的风格图片url
   *
   * @generated from field: string sref_image_url = 15;
   */
  srefImageUrl: string;

  /**
   * emoji生图依赖的controlnet cannny 图片url
   *
   * @generated from field: string canny_image_url = 16;
   */
  cannyImageUrl: string;

  /**
   * emoji mask controlnet图片url
   *
   * @generated from field: string mask_image_url = 17;
   */
  maskImageUrl: string;

  /**
   * emoji dwpose controlnet图片url
   *
   * @generated from field: string dwpose_image_url = 18;
   */
  dwposeImageUrl: string;

  /**
   * 强制使用动态lora workflow加载方案
   *
   * @generated from field: bool force_dymaic_lora = 19;
   */
  forceDymaicLora: boolean;

  /**
   * 指定需要的场景的模型版本
   *
   * @generated from field: uint32 model_version = 20;
   */
  modelVersion: number;

  constructor(data?: PartialMessage<GenImageV2Req>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.GenImageV2Req";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenImageV2Req;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenImageV2Req;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenImageV2Req;

  static equals(a: GenImageV2Req | PlainMessage<GenImageV2Req> | undefined, b: GenImageV2Req | PlainMessage<GenImageV2Req> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.GenImageV3Req
 */
export declare class GenImageV3Req extends Message<GenImageV3Req> {
  /**
   * 风格
   *
   * @generated from field: string style = 1;
   */
  style: string;

  /**
   * 玩法
   *
   * @generated from field: step.raccoon.common.GameType game_type = 2;
   */
  gameType: GameType;

  /**
   * @generated from field: step.raccoon.imagegen.ImageGenMode mode = 3;
   */
  mode: ImageGenMode;

  /**
   * ip角色列表
   *
   * @generated from field: repeated step.raccoon.imagegen.Role roles = 4;
   */
  roles: Role[];

  /**
   * json dict string, 指定mode下，额外需要的生图参数。目前只有双人生图模型，可选填双人interact:prompt.....
   *
   * @generated from field: string extra = 5;
   */
  extra: string;

  /**
   *  每个角色专属的生图英文prompt
   *
   * @generated from field: repeated string prompts = 6;
   */
  prompts: string[];

  /**
   * @generated from field: int32 width = 7;
   */
  width: number;

  /**
   * @generated from field: int32 height = 8;
   */
  height: number;

  /**
   * 图片数量
   *
   * @generated from field: int32 n = 9;
   */
  n: number;

  /**
   * 是否并行生成，默认为true，后端最多单次3个并行生图
   *
   * @generated from field: bool paralle = 10;
   */
  paralle: boolean;

  /**
   * 路由到多个模型时的模型生度调度策略，默认为ROUND_ROBIN
   *
   * @generated from field: step.raccoon.imagegen.ModelSchedStrategy strategy = 11;
   */
  strategy: ModelSchedStrategy;

  /**
   * 业务侧需要透传的生图模型参数，默认为空，json dict string，请严格根据业务需要透传，具体参考stepcast的生图接口文档
   *
   * @generated from field: string params = 12;
   */
  params: string;

  /**
   * 路由到超分模型时，是否进行超分（true时，超分模型会默认将size进行x2scale
   *
   * @generated from field: bool upscale = 13;
   */
  upscale: boolean;

  /**
   * 生图去除角色效果，只保留角色的性别信息
   *
   * @generated from field: bool remove_character = 14;
   */
  removeCharacter: boolean;

  /**
   * sref生图依赖的风格图片url
   *
   * @generated from field: string sref_image_url = 15;
   */
  srefImageUrl: string;

  /**
   * emoji生图依赖的controlnet cannny 图片url
   *
   * @generated from field: string canny_image_url = 16;
   */
  cannyImageUrl: string;

  /**
   * emoji mask controlnet图片url
   *
   * @generated from field: string mask_image_url = 17;
   */
  maskImageUrl: string;

  /**
   * emoji dwpose controlnet图片url
   *
   * @generated from field: string dwpose_image_url = 18;
   */
  dwposeImageUrl: string;

  /**
   * 强制使用动态lora workflow加载方案
   *
   * @generated from field: bool force_dymaic_lora = 19;
   */
  forceDymaicLora: boolean;

  /**
   * 指定需要的场景的模型版本
   *
   * @generated from field: uint32 model_version = 20;
   */
  modelVersion: number;

  constructor(data?: PartialMessage<GenImageV3Req>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.GenImageV3Req";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenImageV3Req;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenImageV3Req;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenImageV3Req;

  static equals(a: GenImageV3Req | PlainMessage<GenImageV3Req> | undefined, b: GenImageV3Req | PlainMessage<GenImageV3Req> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.GenSimpleImageReq
 */
export declare class GenSimpleImageReq extends Message<GenSimpleImageReq> {
  /**
   * 风格
   *
   * @generated from field: string style = 1;
   */
  style: string;

  /**
   * ip角色 空角色ip和角色id都传nil_v2
   *
   * @generated from field: step.raccoon.imagegen.Role role = 2;
   */
  role?: Role;

  /**
   * 生图角色prompt
   *
   * @generated from field: string prompt = 3;
   */
  prompt: string;

  /**
   * @generated from field: int32 width = 4;
   */
  width: number;

  /**
   * @generated from field: int32 height = 5;
   */
  height: number;

  /**
   * 图片数量
   *
   * @generated from field: int32 n = 6;
   */
  n: number;

  /**
   * sref生图依赖的风格图片url
   *
   * @generated from field: string sref_image_url = 7;
   */
  srefImageUrl: string;

  constructor(data?: PartialMessage<GenSimpleImageReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.GenSimpleImageReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenSimpleImageReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenSimpleImageReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenSimpleImageReq;

  static equals(a: GenSimpleImageReq | PlainMessage<GenSimpleImageReq> | undefined, b: GenSimpleImageReq | PlainMessage<GenSimpleImageReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.AnimeInfo
 */
export declare class AnimeInfo extends Message<AnimeInfo> {
  /**
   * 画面类型
   *
   * @generated from field: step.raccoon.imagegen.AnimeType anime_type = 1;
   */
  animeType: AnimeType;

  constructor(data?: PartialMessage<AnimeInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.AnimeInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AnimeInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AnimeInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AnimeInfo;

  static equals(a: AnimeInfo | PlainMessage<AnimeInfo> | undefined, b: AnimeInfo | PlainMessage<AnimeInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.IdentifyAnimeReq
 */
export declare class IdentifyAnimeReq extends Message<IdentifyAnimeReq> {
  /**
   * 图片
   *
   * @generated from field: repeated step.raccoon.common.InternalImage images = 1;
   */
  images: InternalImage[];

  constructor(data?: PartialMessage<IdentifyAnimeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.IdentifyAnimeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IdentifyAnimeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IdentifyAnimeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IdentifyAnimeReq;

  static equals(a: IdentifyAnimeReq | PlainMessage<IdentifyAnimeReq> | undefined, b: IdentifyAnimeReq | PlainMessage<IdentifyAnimeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.IdentifyAnimeResp
 */
export declare class IdentifyAnimeResp extends Message<IdentifyAnimeResp> {
  /**
   * 判定结果
   *
   * @generated from field: repeated step.raccoon.imagegen.AnimeInfo result = 1;
   */
  result: AnimeInfo[];

  constructor(data?: PartialMessage<IdentifyAnimeResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.IdentifyAnimeResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IdentifyAnimeResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IdentifyAnimeResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IdentifyAnimeResp;

  static equals(a: IdentifyAnimeResp | PlainMessage<IdentifyAnimeResp> | undefined, b: IdentifyAnimeResp | PlainMessage<IdentifyAnimeResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.IdentifyWatermarksReq
 */
export declare class IdentifyWatermarksReq extends Message<IdentifyWatermarksReq> {
  /**
   * @generated from field: repeated step.raccoon.common.InternalImage images = 1;
   */
  images: InternalImage[];

  constructor(data?: PartialMessage<IdentifyWatermarksReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.IdentifyWatermarksReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IdentifyWatermarksReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IdentifyWatermarksReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IdentifyWatermarksReq;

  static equals(a: IdentifyWatermarksReq | PlainMessage<IdentifyWatermarksReq> | undefined, b: IdentifyWatermarksReq | PlainMessage<IdentifyWatermarksReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.IdentifyWatermarksResp
 */
export declare class IdentifyWatermarksResp extends Message<IdentifyWatermarksResp> {
  /**
   * @generated from field: repeated step.raccoon.imagegen.Rectangles result = 1;
   */
  result: Rectangles[];

  constructor(data?: PartialMessage<IdentifyWatermarksResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.IdentifyWatermarksResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IdentifyWatermarksResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IdentifyWatermarksResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IdentifyWatermarksResp;

  static equals(a: IdentifyWatermarksResp | PlainMessage<IdentifyWatermarksResp> | undefined, b: IdentifyWatermarksResp | PlainMessage<IdentifyWatermarksResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.Rectangle
 */
export declare class Rectangle extends Message<Rectangle> {
  /**
   * 左上x坐标点
   *
   * @generated from field: int32 start_x = 1;
   */
  startX: number;

  /**
   * 左上y坐标点
   *
   * @generated from field: int32 start_y = 2;
   */
  startY: number;

  /**
   * 右下x坐标点
   *
   * @generated from field: int32 end_x = 3;
   */
  endX: number;

  /**
   * 右下y坐标点
   *
   * @generated from field: int32 end_y = 4;
   */
  endY: number;

  constructor(data?: PartialMessage<Rectangle>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.Rectangle";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Rectangle;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Rectangle;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Rectangle;

  static equals(a: Rectangle | PlainMessage<Rectangle> | undefined, b: Rectangle | PlainMessage<Rectangle> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.Rectangles
 */
export declare class Rectangles extends Message<Rectangles> {
  /**
   * @generated from field: repeated step.raccoon.imagegen.Rectangle items = 1;
   */
  items: Rectangle[];

  constructor(data?: PartialMessage<Rectangles>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.Rectangles";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Rectangles;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Rectangles;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Rectangles;

  static equals(a: Rectangles | PlainMessage<Rectangles> | undefined, b: Rectangles | PlainMessage<Rectangles> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.IdentifyFacesReq
 */
export declare class IdentifyFacesReq extends Message<IdentifyFacesReq> {
  /**
   * @generated from field: repeated step.raccoon.common.InternalImage images = 1;
   */
  images: InternalImage[];

  constructor(data?: PartialMessage<IdentifyFacesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.IdentifyFacesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IdentifyFacesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IdentifyFacesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IdentifyFacesReq;

  static equals(a: IdentifyFacesReq | PlainMessage<IdentifyFacesReq> | undefined, b: IdentifyFacesReq | PlainMessage<IdentifyFacesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.IdentifyFacesResp
 */
export declare class IdentifyFacesResp extends Message<IdentifyFacesResp> {
  /**
   * @generated from field: repeated step.raccoon.imagegen.Rectangles result = 1;
   */
  result: Rectangles[];

  constructor(data?: PartialMessage<IdentifyFacesResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.IdentifyFacesResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IdentifyFacesResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IdentifyFacesResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IdentifyFacesResp;

  static equals(a: IdentifyFacesResp | PlainMessage<IdentifyFacesResp> | undefined, b: IdentifyFacesResp | PlainMessage<IdentifyFacesResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.IdentifyAestheticsReq
 */
export declare class IdentifyAestheticsReq extends Message<IdentifyAestheticsReq> {
  /**
   * @generated from field: repeated step.raccoon.common.InternalImage images = 1;
   */
  images: InternalImage[];

  constructor(data?: PartialMessage<IdentifyAestheticsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.IdentifyAestheticsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IdentifyAestheticsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IdentifyAestheticsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IdentifyAestheticsReq;

  static equals(a: IdentifyAestheticsReq | PlainMessage<IdentifyAestheticsReq> | undefined, b: IdentifyAestheticsReq | PlainMessage<IdentifyAestheticsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.imagegen.IdentifyAestheticsResp
 */
export declare class IdentifyAestheticsResp extends Message<IdentifyAestheticsResp> {
  /**
   * @generated from field: repeated float result = 1;
   */
  result: number[];

  constructor(data?: PartialMessage<IdentifyAestheticsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.imagegen.IdentifyAestheticsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IdentifyAestheticsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IdentifyAestheticsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IdentifyAestheticsResp;

  static equals(a: IdentifyAestheticsResp | PlainMessage<IdentifyAestheticsResp> | undefined, b: IdentifyAestheticsResp | PlainMessage<IdentifyAestheticsResp> | undefined): boolean;
}

