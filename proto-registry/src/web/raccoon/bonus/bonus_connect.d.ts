// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/bonus/bonus.proto (package step.raccoon.bonus, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CheckInRemindReq, CheckInRemindResp, CheckInReq, CheckInResp, DoDrawReq, DoDrawResp, FindAchievementTasksReq, FindAchievementTasksResp, FindCheckInInfosReq, FindCheckInInfosResp, FindDrawCheckInTasksReq, FindDrawCheckInTasksResp, FindGroupTaskReq, FindGroupTaskResp, FindNormalCheckInTaskReq, FindNormalCheckInTaskResp, FindNormalTasksReq, FindNormalTasksResp, FindRecordsReq, FindRecordsResp, GetBonusBackpackReq, GetBonusBackpackResp, GetDailyTarotHistoryReq, GetDailyTarotHistoryResp, GetDailyTarotReq, GetDailyTarotResp, GetOtherCheckInTaskRemindReq, GetOtherCheckInTaskRemindResp, GetRewardPoolReq, GetRewardPoolResp, GetTaskRewardReq, GetTaskRewardResp, UpdateRecordReq, UpdateRecordResp } from "./bonus_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.bonus.Bonus
 */
export declare const Bonus: {
  readonly typeName: "step.raccoon.bonus.Bonus",
  readonly methods: {
    /**
     * 查询常规签到任务
     *
     * @generated from rpc step.raccoon.bonus.Bonus.FindNormalCheckInTask
     */
    readonly findNormalCheckInTask: {
      readonly name: "FindNormalCheckInTask",
      readonly I: typeof FindNormalCheckInTaskReq,
      readonly O: typeof FindNormalCheckInTaskResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询签到信息
     *
     * @generated from rpc step.raccoon.bonus.Bonus.FindCheckInInfos
     */
    readonly findCheckInInfos: {
      readonly name: "FindCheckInInfos",
      readonly I: typeof FindCheckInInfosReq,
      readonly O: typeof FindCheckInInfosResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 签到并领取常规签到奖励(如果有)
     *
     * @generated from rpc step.raccoon.bonus.Bonus.CheckIn
     */
    readonly checkIn: {
      readonly name: "CheckIn",
      readonly I: typeof CheckInReq,
      readonly O: typeof CheckInResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 常规签到联动可完成的其他签到任务提示
     *
     * @generated from rpc step.raccoon.bonus.Bonus.GetOtherCheckInTaskRemind
     */
    readonly getOtherCheckInTaskRemind: {
      readonly name: "GetOtherCheckInTaskRemind",
      readonly I: typeof GetOtherCheckInTaskRemindReq,
      readonly O: typeof GetOtherCheckInTaskRemindResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 今日塔罗牌
     *
     * @generated from rpc step.raccoon.bonus.Bonus.GetDailyTarot
     */
    readonly getDailyTarot: {
      readonly name: "GetDailyTarot",
      readonly I: typeof GetDailyTarotReq,
      readonly O: typeof GetDailyTarotResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 塔罗牌历史
     *
     * @generated from rpc step.raccoon.bonus.Bonus.GetDailyTarotHistory
     */
    readonly getDailyTarotHistory: {
      readonly name: "GetDailyTarotHistory",
      readonly I: typeof GetDailyTarotHistoryReq,
      readonly O: typeof GetDailyTarotHistoryResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询进度条任务
     *
     * @generated from rpc step.raccoon.bonus.Bonus.FindGroupTask
     */
    readonly findGroupTask: {
      readonly name: "FindGroupTask",
      readonly I: typeof FindGroupTaskReq,
      readonly O: typeof FindGroupTaskResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询常规任务列表
     *
     * @generated from rpc step.raccoon.bonus.Bonus.FindNormalTasks
     */
    readonly findNormalTasks: {
      readonly name: "FindNormalTasks",
      readonly I: typeof FindNormalTasksReq,
      readonly O: typeof FindNormalTasksResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 领取任务奖励
     *
     * @generated from rpc step.raccoon.bonus.Bonus.GetTaskReward
     */
    readonly getTaskReward: {
      readonly name: "GetTaskReward",
      readonly I: typeof GetTaskRewardReq,
      readonly O: typeof GetTaskRewardResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 签到提醒订阅
     *
     * @generated from rpc step.raccoon.bonus.Bonus.CheckInRemind
     */
    readonly checkInRemind: {
      readonly name: "CheckInRemind",
      readonly I: typeof CheckInRemindReq,
      readonly O: typeof CheckInRemindResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取活动中心历史记录
     *
     * @generated from rpc step.raccoon.bonus.Bonus.FindRecords
     */
    readonly findRecords: {
      readonly name: "FindRecords",
      readonly I: typeof FindRecordsReq,
      readonly O: typeof FindRecordsResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取抽奖奖池列表
     *
     * @generated from rpc step.raccoon.bonus.Bonus.GetRewardPool
     */
    readonly getRewardPool: {
      readonly name: "GetRewardPool",
      readonly I: typeof GetRewardPoolReq,
      readonly O: typeof GetRewardPoolResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 查询签到抽奖任务
     *
     * @generated from rpc step.raccoon.bonus.Bonus.FindDrawCheckInTasks
     */
    readonly findDrawCheckInTasks: {
      readonly name: "FindDrawCheckInTasks",
      readonly I: typeof FindDrawCheckInTasksReq,
      readonly O: typeof FindDrawCheckInTasksResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取活动中心物品信息
     *
     * @generated from rpc step.raccoon.bonus.Bonus.GetBonusBackpack
     */
    readonly getBonusBackpack: {
      readonly name: "GetBonusBackpack",
      readonly I: typeof GetBonusBackpackReq,
      readonly O: typeof GetBonusBackpackResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 抽奖
     *
     * @generated from rpc step.raccoon.bonus.Bonus.DoDraw
     */
    readonly doDraw: {
      readonly name: "DoDraw",
      readonly I: typeof DoDrawReq,
      readonly O: typeof DoDrawResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 更新记录信息 填写领奖地址
     *
     * @generated from rpc step.raccoon.bonus.Bonus.UpdateRecord
     */
    readonly updateRecord: {
      readonly name: "UpdateRecord",
      readonly I: typeof UpdateRecordReq,
      readonly O: typeof UpdateRecordResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * 获取成就任务
     *
     * @generated from rpc step.raccoon.bonus.Bonus.FindAchievementTasks
     */
    readonly findAchievementTasks: {
      readonly name: "FindAchievementTasks",
      readonly I: typeof FindAchievementTasksReq,
      readonly O: typeof FindAchievementTasksResp,
      readonly kind: MethodKind.Unary,
    },
  }
};

