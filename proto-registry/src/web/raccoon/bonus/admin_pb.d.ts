// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bonus/admin.proto (package step.raccoon.bonus, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Pagination } from "../common/utils_pb.js";
import type { BehaviorType, CheckInTaskType, ConfigState, DrawCheckInTargetType, NormalTargetType, TaskType } from "./common_pb.js";
import type { GameType, RewardType } from "../common/types_pb.js";

/**
 * @generated from message step.raccoon.bonus.FindCheckInConfigsReq
 */
export declare class FindCheckInConfigsReq extends Message<FindCheckInConfigsReq> {
  /**
   * @generated from field: step.raccoon.bonus.FindCheckInConfigsReq.FindCheckInConfigsParam search = 1;
   */
  search?: FindCheckInConfigsReq_FindCheckInConfigsParam;

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindCheckInConfigsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindCheckInConfigsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindCheckInConfigsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindCheckInConfigsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindCheckInConfigsReq;

  static equals(a: FindCheckInConfigsReq | PlainMessage<FindCheckInConfigsReq> | undefined, b: FindCheckInConfigsReq | PlainMessage<FindCheckInConfigsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindCheckInConfigsReq.FindCheckInConfigsParam
 */
export declare class FindCheckInConfigsReq_FindCheckInConfigsParam extends Message<FindCheckInConfigsReq_FindCheckInConfigsParam> {
  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 1;
   */
  state?: ConfigState;

  /**
   * @generated from field: optional step.raccoon.bonus.CheckInTaskType check_in_task_type = 11;
   */
  checkInTaskType?: CheckInTaskType;

  constructor(data?: PartialMessage<FindCheckInConfigsReq_FindCheckInConfigsParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindCheckInConfigsReq.FindCheckInConfigsParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindCheckInConfigsReq_FindCheckInConfigsParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindCheckInConfigsReq_FindCheckInConfigsParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindCheckInConfigsReq_FindCheckInConfigsParam;

  static equals(a: FindCheckInConfigsReq_FindCheckInConfigsParam | PlainMessage<FindCheckInConfigsReq_FindCheckInConfigsParam> | undefined, b: FindCheckInConfigsReq_FindCheckInConfigsParam | PlainMessage<FindCheckInConfigsReq_FindCheckInConfigsParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.RewardAdmin
 */
export declare class RewardAdmin extends Message<RewardAdmin> {
  /**
   * @generated from field: string image_media_id = 1;
   */
  imageMediaId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: uint32 count = 4;
   */
  count: number;

  /**
   * @generated from field: step.raccoon.common.RewardType reward_type = 11;
   */
  rewardType: RewardType;

  /**
   * @generated from field: string right_key = 12;
   */
  rightKey: string;

  /**
   * @generated from field: string image_url = 101;
   */
  imageUrl: string;

  constructor(data?: PartialMessage<RewardAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.RewardAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardAdmin;

  static equals(a: RewardAdmin | PlainMessage<RewardAdmin> | undefined, b: RewardAdmin | PlainMessage<RewardAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInNormalTargetAdmin
 */
export declare class CheckInNormalTargetAdmin extends Message<CheckInNormalTargetAdmin> {
  /**
   * @generated from field: step.raccoon.bonus.RewardAdmin reward = 1;
   */
  reward?: RewardAdmin;

  constructor(data?: PartialMessage<CheckInNormalTargetAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInNormalTargetAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInNormalTargetAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInNormalTargetAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInNormalTargetAdmin;

  static equals(a: CheckInNormalTargetAdmin | PlainMessage<CheckInNormalTargetAdmin> | undefined, b: CheckInNormalTargetAdmin | PlainMessage<CheckInNormalTargetAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInNormalTaskAdmin
 */
export declare class CheckInNormalTaskAdmin extends Message<CheckInNormalTaskAdmin> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: uint32 effected_at = 3;
   */
  effectedAt: number;

  /**
   * @generated from field: uint32 expired_at = 4;
   */
  expiredAt: number;

  /**
   * @generated from field: repeated step.raccoon.bonus.CheckInNormalTargetAdmin targets = 11;
   */
  targets: CheckInNormalTargetAdmin[];

  constructor(data?: PartialMessage<CheckInNormalTaskAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInNormalTaskAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInNormalTaskAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInNormalTaskAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInNormalTaskAdmin;

  static equals(a: CheckInNormalTaskAdmin | PlainMessage<CheckInNormalTaskAdmin> | undefined, b: CheckInNormalTaskAdmin | PlainMessage<CheckInNormalTaskAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInDrawTargetAdmin
 */
export declare class CheckInDrawTargetAdmin extends Message<CheckInDrawTargetAdmin> {
  /**
   * @generated from field: step.raccoon.bonus.RewardAdmin reward = 1;
   */
  reward?: RewardAdmin;

  /**
   * @generated from field: uint32 target_count = 2;
   */
  targetCount: number;

  /**
   * @generated from field: uint32 fixed_date = 3;
   */
  fixedDate: number;

  /**
   * @generated from field: step.raccoon.bonus.DrawCheckInTargetType target_type = 101;
   */
  targetType: DrawCheckInTargetType;

  constructor(data?: PartialMessage<CheckInDrawTargetAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInDrawTargetAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInDrawTargetAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInDrawTargetAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInDrawTargetAdmin;

  static equals(a: CheckInDrawTargetAdmin | PlainMessage<CheckInDrawTargetAdmin> | undefined, b: CheckInDrawTargetAdmin | PlainMessage<CheckInDrawTargetAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInDrawTaskAdmin
 */
export declare class CheckInDrawTaskAdmin extends Message<CheckInDrawTaskAdmin> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: uint32 effected_at = 3;
   */
  effectedAt: number;

  /**
   * @generated from field: uint32 expired_at = 4;
   */
  expiredAt: number;

  /**
   * @generated from field: repeated step.raccoon.bonus.CheckInDrawTargetAdmin targets = 11;
   */
  targets: CheckInDrawTargetAdmin[];

  constructor(data?: PartialMessage<CheckInDrawTaskAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInDrawTaskAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInDrawTaskAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInDrawTaskAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInDrawTaskAdmin;

  static equals(a: CheckInDrawTaskAdmin | PlainMessage<CheckInDrawTaskAdmin> | undefined, b: CheckInDrawTaskAdmin | PlainMessage<CheckInDrawTaskAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInConfig
 */
export declare class CheckInConfig extends Message<CheckInConfig> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: step.raccoon.bonus.ConfigState state = 2;
   */
  state: ConfigState;

  /**
   * @generated from field: step.raccoon.bonus.CheckInTaskType check_in_task_type = 11;
   */
  checkInTaskType: CheckInTaskType;

  /**
   * @generated from field: step.raccoon.bonus.CheckInNormalTaskAdmin normal = 21;
   */
  normal?: CheckInNormalTaskAdmin;

  /**
   * @generated from field: step.raccoon.bonus.CheckInDrawTaskAdmin draw = 22;
   */
  draw?: CheckInDrawTaskAdmin;

  /**
   * @generated from field: string updater = 101;
   */
  updater: string;

  /**
   * @generated from field: string created_at = 111;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 112;
   */
  updatedAt: string;

  constructor(data?: PartialMessage<CheckInConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInConfig;

  static equals(a: CheckInConfig | PlainMessage<CheckInConfig> | undefined, b: CheckInConfig | PlainMessage<CheckInConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindCheckInConfigsResp
 */
export declare class FindCheckInConfigsResp extends Message<FindCheckInConfigsResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.CheckInConfig configs = 1;
   */
  configs: CheckInConfig[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindCheckInConfigsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindCheckInConfigsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindCheckInConfigsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindCheckInConfigsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindCheckInConfigsResp;

  static equals(a: FindCheckInConfigsResp | PlainMessage<FindCheckInConfigsResp> | undefined, b: FindCheckInConfigsResp | PlainMessage<FindCheckInConfigsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetCheckInConfigReq
 */
export declare class GetCheckInConfigReq extends Message<GetCheckInConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  constructor(data?: PartialMessage<GetCheckInConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetCheckInConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCheckInConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCheckInConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCheckInConfigReq;

  static equals(a: GetCheckInConfigReq | PlainMessage<GetCheckInConfigReq> | undefined, b: GetCheckInConfigReq | PlainMessage<GetCheckInConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetCheckInConfigResp
 */
export declare class GetCheckInConfigResp extends Message<GetCheckInConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.CheckInConfig config = 1;
   */
  config?: CheckInConfig;

  constructor(data?: PartialMessage<GetCheckInConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetCheckInConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCheckInConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCheckInConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCheckInConfigResp;

  static equals(a: GetCheckInConfigResp | PlainMessage<GetCheckInConfigResp> | undefined, b: GetCheckInConfigResp | PlainMessage<GetCheckInConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateCheckInConfigReq
 */
export declare class CreateCheckInConfigReq extends Message<CreateCheckInConfigReq> {
  /**
   * @generated from field: step.raccoon.bonus.CheckInConfig config = 1;
   */
  config?: CheckInConfig;

  constructor(data?: PartialMessage<CreateCheckInConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateCheckInConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateCheckInConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateCheckInConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateCheckInConfigReq;

  static equals(a: CreateCheckInConfigReq | PlainMessage<CreateCheckInConfigReq> | undefined, b: CreateCheckInConfigReq | PlainMessage<CreateCheckInConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateCheckInConfigResp
 */
export declare class CreateCheckInConfigResp extends Message<CreateCheckInConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.CheckInConfig config = 1;
   */
  config?: CheckInConfig;

  constructor(data?: PartialMessage<CreateCheckInConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateCheckInConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateCheckInConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateCheckInConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateCheckInConfigResp;

  static equals(a: CreateCheckInConfigResp | PlainMessage<CreateCheckInConfigResp> | undefined, b: CreateCheckInConfigResp | PlainMessage<CreateCheckInConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateCheckInConfigReq
 */
export declare class UpdateCheckInConfigReq extends Message<UpdateCheckInConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 2;
   */
  state?: ConfigState;

  /**
   * @generated from field: optional step.raccoon.bonus.CheckInTaskType check_in_task_type = 11;
   */
  checkInTaskType?: CheckInTaskType;

  /**
   * @generated from field: optional step.raccoon.bonus.CheckInNormalTaskAdmin normal = 21;
   */
  normal?: CheckInNormalTaskAdmin;

  /**
   * @generated from field: optional step.raccoon.bonus.CheckInDrawTaskAdmin draw = 22;
   */
  draw?: CheckInDrawTaskAdmin;

  constructor(data?: PartialMessage<UpdateCheckInConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateCheckInConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateCheckInConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateCheckInConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateCheckInConfigReq;

  static equals(a: UpdateCheckInConfigReq | PlainMessage<UpdateCheckInConfigReq> | undefined, b: UpdateCheckInConfigReq | PlainMessage<UpdateCheckInConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateCheckInConfigResp
 */
export declare class UpdateCheckInConfigResp extends Message<UpdateCheckInConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.CheckInConfig config = 1;
   */
  config?: CheckInConfig;

  constructor(data?: PartialMessage<UpdateCheckInConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateCheckInConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateCheckInConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateCheckInConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateCheckInConfigResp;

  static equals(a: UpdateCheckInConfigResp | PlainMessage<UpdateCheckInConfigResp> | undefined, b: UpdateCheckInConfigResp | PlainMessage<UpdateCheckInConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindTaskConfigsReq
 */
export declare class FindTaskConfigsReq extends Message<FindTaskConfigsReq> {
  /**
   * @generated from field: step.raccoon.bonus.FindTaskConfigsReq.FindTaskConfigsParam search = 1;
   */
  search?: FindTaskConfigsReq_FindTaskConfigsParam;

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindTaskConfigsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindTaskConfigsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindTaskConfigsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindTaskConfigsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindTaskConfigsReq;

  static equals(a: FindTaskConfigsReq | PlainMessage<FindTaskConfigsReq> | undefined, b: FindTaskConfigsReq | PlainMessage<FindTaskConfigsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindTaskConfigsReq.FindTaskConfigsParam
 */
export declare class FindTaskConfigsReq_FindTaskConfigsParam extends Message<FindTaskConfigsReq_FindTaskConfigsParam> {
  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 1;
   */
  state?: ConfigState;

  /**
   * @generated from field: optional step.raccoon.bonus.TaskType task_type = 11;
   */
  taskType?: TaskType;

  constructor(data?: PartialMessage<FindTaskConfigsReq_FindTaskConfigsParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindTaskConfigsReq.FindTaskConfigsParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindTaskConfigsReq_FindTaskConfigsParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindTaskConfigsReq_FindTaskConfigsParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindTaskConfigsReq_FindTaskConfigsParam;

  static equals(a: FindTaskConfigsReq_FindTaskConfigsParam | PlainMessage<FindTaskConfigsReq_FindTaskConfigsParam> | undefined, b: FindTaskConfigsReq_FindTaskConfigsParam | PlainMessage<FindTaskConfigsReq_FindTaskConfigsParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.NormalTaskAdmin
 */
export declare class NormalTaskAdmin extends Message<NormalTaskAdmin> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: uint32 effected_at = 3;
   */
  effectedAt: number;

  /**
   * @generated from field: uint32 expired_at = 4;
   */
  expiredAt: number;

  /**
   * @generated from field: uint32 weighting = 5;
   */
  weighting: number;

  /**
   * @generated from field: string cron_expression = 6;
   */
  cronExpression: string;

  /**
   * @generated from field: repeated step.raccoon.bonus.NormalTaskAdmin.TargetAdmin targets = 21;
   */
  targets: NormalTaskAdmin_TargetAdmin[];

  /**
   * @generated from field: string redirect_link = 22;
   */
  redirectLink: string;

  constructor(data?: PartialMessage<NormalTaskAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.NormalTaskAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NormalTaskAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NormalTaskAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NormalTaskAdmin;

  static equals(a: NormalTaskAdmin | PlainMessage<NormalTaskAdmin> | undefined, b: NormalTaskAdmin | PlainMessage<NormalTaskAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.NormalTaskAdmin.TargetAdmin
 */
export declare class NormalTaskAdmin_TargetAdmin extends Message<NormalTaskAdmin_TargetAdmin> {
  /**
   * @generated from field: step.raccoon.bonus.RewardAdmin reward = 1;
   */
  reward?: RewardAdmin;

  /**
   * @generated from field: repeated step.raccoon.common.GameType allow_game_types = 2;
   */
  allowGameTypes: GameType[];

  /**
   * @generated from field: repeated string allow_topic_ids = 3;
   */
  allowTopicIds: string[];

  /**
   * @generated from field: uint32 target_count = 51;
   */
  targetCount: number;

  /**
   * @generated from field: step.raccoon.bonus.NormalTargetType target_type = 101;
   */
  targetType: NormalTargetType;

  constructor(data?: PartialMessage<NormalTaskAdmin_TargetAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.NormalTaskAdmin.TargetAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NormalTaskAdmin_TargetAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NormalTaskAdmin_TargetAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NormalTaskAdmin_TargetAdmin;

  static equals(a: NormalTaskAdmin_TargetAdmin | PlainMessage<NormalTaskAdmin_TargetAdmin> | undefined, b: NormalTaskAdmin_TargetAdmin | PlainMessage<NormalTaskAdmin_TargetAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GroupTaskAdmin
 */
export declare class GroupTaskAdmin extends Message<GroupTaskAdmin> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: uint32 effected_at = 3;
   */
  effectedAt: number;

  /**
   * @generated from field: uint32 expired_at = 4;
   */
  expiredAt: number;

  /**
   * @generated from field: string cron_expression = 5;
   */
  cronExpression: string;

  /**
   * @generated from field: repeated step.raccoon.bonus.GroupTaskAdmin.TargetAdmin targets = 21;
   */
  targets: GroupTaskAdmin_TargetAdmin[];

  constructor(data?: PartialMessage<GroupTaskAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GroupTaskAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GroupTaskAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GroupTaskAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GroupTaskAdmin;

  static equals(a: GroupTaskAdmin | PlainMessage<GroupTaskAdmin> | undefined, b: GroupTaskAdmin | PlainMessage<GroupTaskAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GroupTaskAdmin.TargetAdmin
 */
export declare class GroupTaskAdmin_TargetAdmin extends Message<GroupTaskAdmin_TargetAdmin> {
  /**
   * @generated from field: step.raccoon.bonus.RewardAdmin reward = 1;
   */
  reward?: RewardAdmin;

  /**
   * @generated from field: uint32 target_count = 11;
   */
  targetCount: number;

  constructor(data?: PartialMessage<GroupTaskAdmin_TargetAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GroupTaskAdmin.TargetAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GroupTaskAdmin_TargetAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GroupTaskAdmin_TargetAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GroupTaskAdmin_TargetAdmin;

  static equals(a: GroupTaskAdmin_TargetAdmin | PlainMessage<GroupTaskAdmin_TargetAdmin> | undefined, b: GroupTaskAdmin_TargetAdmin | PlainMessage<GroupTaskAdmin_TargetAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.TaskAdmin
 */
export declare class TaskAdmin extends Message<TaskAdmin> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: uint32 effected_at = 3;
   */
  effectedAt: number;

  /**
   * @generated from field: uint32 expired_at = 4;
   */
  expiredAt: number;

  /**
   * @generated from field: uint32 weighting = 5;
   */
  weighting: number;

  /**
   * @generated from field: string cron_expression = 6;
   */
  cronExpression: string;

  /**
   * @generated from field: repeated step.raccoon.bonus.TaskAdmin.TargetAdmin targets = 21;
   */
  targets: TaskAdmin_TargetAdmin[];

  /**
   * @generated from field: string redirect_link = 22;
   */
  redirectLink: string;

  constructor(data?: PartialMessage<TaskAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.TaskAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskAdmin;

  static equals(a: TaskAdmin | PlainMessage<TaskAdmin> | undefined, b: TaskAdmin | PlainMessage<TaskAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.TaskAdmin.NormalTargetOption
 */
export declare class TaskAdmin_NormalTargetOption extends Message<TaskAdmin_NormalTargetOption> {
  /**
   * @generated from field: step.raccoon.bonus.NormalTargetType target_type = 1;
   */
  targetType: NormalTargetType;

  /**
   * @generated from field: repeated uint32 allow_game_types = 2;
   */
  allowGameTypes: number[];

  /**
   * @generated from field: repeated string allow_topic_ids = 3;
   */
  allowTopicIds: string[];

  constructor(data?: PartialMessage<TaskAdmin_NormalTargetOption>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.TaskAdmin.NormalTargetOption";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskAdmin_NormalTargetOption;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskAdmin_NormalTargetOption;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskAdmin_NormalTargetOption;

  static equals(a: TaskAdmin_NormalTargetOption | PlainMessage<TaskAdmin_NormalTargetOption> | undefined, b: TaskAdmin_NormalTargetOption | PlainMessage<TaskAdmin_NormalTargetOption> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.TaskAdmin.TargetAdmin
 */
export declare class TaskAdmin_TargetAdmin extends Message<TaskAdmin_TargetAdmin> {
  /**
   * @generated from field: step.raccoon.bonus.RewardAdmin reward = 1;
   */
  reward?: RewardAdmin;

  /**
   * @generated from field: repeated step.raccoon.bonus.RewardAdmin multi_rewards = 2;
   */
  multiRewards: RewardAdmin[];

  /**
   * @generated from field: uint32 target_count = 11;
   */
  targetCount: number;

  /**
   * @generated from field: string behavior_id = 51;
   */
  behaviorId: string;

  /**
   * @generated from field: step.raccoon.bonus.TaskAdmin.NormalTargetOption normal_target_option = 101;
   */
  normalTargetOption?: TaskAdmin_NormalTargetOption;

  constructor(data?: PartialMessage<TaskAdmin_TargetAdmin>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.TaskAdmin.TargetAdmin";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskAdmin_TargetAdmin;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskAdmin_TargetAdmin;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskAdmin_TargetAdmin;

  static equals(a: TaskAdmin_TargetAdmin | PlainMessage<TaskAdmin_TargetAdmin> | undefined, b: TaskAdmin_TargetAdmin | PlainMessage<TaskAdmin_TargetAdmin> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.TaskConfig
 */
export declare class TaskConfig extends Message<TaskConfig> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: step.raccoon.bonus.ConfigState state = 2;
   */
  state: ConfigState;

  /**
   * @generated from field: step.raccoon.bonus.TaskType task_type = 11;
   */
  taskType: TaskType;

  /**
   * @generated from field: step.raccoon.bonus.NormalTaskAdmin normal = 21;
   */
  normal?: NormalTaskAdmin;

  /**
   * @generated from field: step.raccoon.bonus.GroupTaskAdmin group = 22;
   */
  group?: GroupTaskAdmin;

  /**
   * @generated from field: step.raccoon.bonus.TaskAdmin task = 51;
   */
  task?: TaskAdmin;

  /**
   * @generated from field: string updater = 101;
   */
  updater: string;

  /**
   * @generated from field: string created_at = 111;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 112;
   */
  updatedAt: string;

  constructor(data?: PartialMessage<TaskConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.TaskConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskConfig;

  static equals(a: TaskConfig | PlainMessage<TaskConfig> | undefined, b: TaskConfig | PlainMessage<TaskConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindTaskConfigsResp
 */
export declare class FindTaskConfigsResp extends Message<FindTaskConfigsResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.TaskConfig configs = 1;
   */
  configs: TaskConfig[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindTaskConfigsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindTaskConfigsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindTaskConfigsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindTaskConfigsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindTaskConfigsResp;

  static equals(a: FindTaskConfigsResp | PlainMessage<FindTaskConfigsResp> | undefined, b: FindTaskConfigsResp | PlainMessage<FindTaskConfigsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetTaskConfigReq
 */
export declare class GetTaskConfigReq extends Message<GetTaskConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  constructor(data?: PartialMessage<GetTaskConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetTaskConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTaskConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTaskConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTaskConfigReq;

  static equals(a: GetTaskConfigReq | PlainMessage<GetTaskConfigReq> | undefined, b: GetTaskConfigReq | PlainMessage<GetTaskConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetTaskConfigResp
 */
export declare class GetTaskConfigResp extends Message<GetTaskConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.TaskConfig config = 1;
   */
  config?: TaskConfig;

  constructor(data?: PartialMessage<GetTaskConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetTaskConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTaskConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTaskConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTaskConfigResp;

  static equals(a: GetTaskConfigResp | PlainMessage<GetTaskConfigResp> | undefined, b: GetTaskConfigResp | PlainMessage<GetTaskConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateTaskConfigReq
 */
export declare class CreateTaskConfigReq extends Message<CreateTaskConfigReq> {
  /**
   * @generated from field: step.raccoon.bonus.TaskConfig config = 1;
   */
  config?: TaskConfig;

  constructor(data?: PartialMessage<CreateTaskConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateTaskConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateTaskConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateTaskConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateTaskConfigReq;

  static equals(a: CreateTaskConfigReq | PlainMessage<CreateTaskConfigReq> | undefined, b: CreateTaskConfigReq | PlainMessage<CreateTaskConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateTaskConfigResp
 */
export declare class CreateTaskConfigResp extends Message<CreateTaskConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.TaskConfig config = 1;
   */
  config?: TaskConfig;

  constructor(data?: PartialMessage<CreateTaskConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateTaskConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateTaskConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateTaskConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateTaskConfigResp;

  static equals(a: CreateTaskConfigResp | PlainMessage<CreateTaskConfigResp> | undefined, b: CreateTaskConfigResp | PlainMessage<CreateTaskConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateTaskConfigReq
 */
export declare class UpdateTaskConfigReq extends Message<UpdateTaskConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 2;
   */
  state?: ConfigState;

  /**
   * @generated from field: optional step.raccoon.bonus.TaskType task_type = 11;
   */
  taskType?: TaskType;

  /**
   * @generated from field: optional step.raccoon.bonus.NormalTaskAdmin normal = 21;
   */
  normal?: NormalTaskAdmin;

  /**
   * @generated from field: optional step.raccoon.bonus.GroupTaskAdmin group = 22;
   */
  group?: GroupTaskAdmin;

  /**
   * @generated from field: optional step.raccoon.bonus.TaskAdmin task = 51;
   */
  task?: TaskAdmin;

  constructor(data?: PartialMessage<UpdateTaskConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateTaskConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTaskConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTaskConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTaskConfigReq;

  static equals(a: UpdateTaskConfigReq | PlainMessage<UpdateTaskConfigReq> | undefined, b: UpdateTaskConfigReq | PlainMessage<UpdateTaskConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateTaskConfigResp
 */
export declare class UpdateTaskConfigResp extends Message<UpdateTaskConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.TaskConfig config = 1;
   */
  config?: TaskConfig;

  constructor(data?: PartialMessage<UpdateTaskConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateTaskConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTaskConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTaskConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTaskConfigResp;

  static equals(a: UpdateTaskConfigResp | PlainMessage<UpdateTaskConfigResp> | undefined, b: UpdateTaskConfigResp | PlainMessage<UpdateTaskConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindRewardPoolConfigsReq
 */
export declare class FindRewardPoolConfigsReq extends Message<FindRewardPoolConfigsReq> {
  /**
   * @generated from field: step.raccoon.bonus.FindRewardPoolConfigsReq.FindRewardPoolConfigsParam search = 1;
   */
  search?: FindRewardPoolConfigsReq_FindRewardPoolConfigsParam;

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindRewardPoolConfigsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindRewardPoolConfigsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindRewardPoolConfigsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindRewardPoolConfigsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindRewardPoolConfigsReq;

  static equals(a: FindRewardPoolConfigsReq | PlainMessage<FindRewardPoolConfigsReq> | undefined, b: FindRewardPoolConfigsReq | PlainMessage<FindRewardPoolConfigsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindRewardPoolConfigsReq.FindRewardPoolConfigsParam
 */
export declare class FindRewardPoolConfigsReq_FindRewardPoolConfigsParam extends Message<FindRewardPoolConfigsReq_FindRewardPoolConfigsParam> {
  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 1;
   */
  state?: ConfigState;

  constructor(data?: PartialMessage<FindRewardPoolConfigsReq_FindRewardPoolConfigsParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindRewardPoolConfigsReq.FindRewardPoolConfigsParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindRewardPoolConfigsReq_FindRewardPoolConfigsParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindRewardPoolConfigsReq_FindRewardPoolConfigsParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindRewardPoolConfigsReq_FindRewardPoolConfigsParam;

  static equals(a: FindRewardPoolConfigsReq_FindRewardPoolConfigsParam | PlainMessage<FindRewardPoolConfigsReq_FindRewardPoolConfigsParam> | undefined, b: FindRewardPoolConfigsReq_FindRewardPoolConfigsParam | PlainMessage<FindRewardPoolConfigsReq_FindRewardPoolConfigsParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.RewardPool
 */
export declare class RewardPool extends Message<RewardPool> {
  /**
   * @generated from field: repeated step.raccoon.bonus.RewardPool.PoolReward pool_rewards = 1;
   */
  poolRewards: RewardPool_PoolReward[];

  constructor(data?: PartialMessage<RewardPool>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.RewardPool";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardPool;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardPool;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardPool;

  static equals(a: RewardPool | PlainMessage<RewardPool> | undefined, b: RewardPool | PlainMessage<RewardPool> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.RewardPool.PoolReward
 */
export declare class RewardPool_PoolReward extends Message<RewardPool_PoolReward> {
  /**
   * @generated from field: step.raccoon.bonus.RewardAdmin reward = 1;
   */
  reward?: RewardAdmin;

  /**
   * @generated from field: uint32 probability = 2;
   */
  probability: number;

  /**
   * @generated from field: string stock_item_config_id = 3;
   */
  stockItemConfigId: string;

  constructor(data?: PartialMessage<RewardPool_PoolReward>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.RewardPool.PoolReward";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardPool_PoolReward;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardPool_PoolReward;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardPool_PoolReward;

  static equals(a: RewardPool_PoolReward | PlainMessage<RewardPool_PoolReward> | undefined, b: RewardPool_PoolReward | PlainMessage<RewardPool_PoolReward> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.RewardPoolConfig
 */
export declare class RewardPoolConfig extends Message<RewardPoolConfig> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: step.raccoon.bonus.ConfigState state = 2;
   */
  state: ConfigState;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @generated from field: step.raccoon.bonus.RewardPool reward_pool = 11;
   */
  rewardPool?: RewardPool;

  /**
   * @generated from field: string updater = 101;
   */
  updater: string;

  /**
   * @generated from field: string created_at = 111;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 112;
   */
  updatedAt: string;

  constructor(data?: PartialMessage<RewardPoolConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.RewardPoolConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardPoolConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardPoolConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardPoolConfig;

  static equals(a: RewardPoolConfig | PlainMessage<RewardPoolConfig> | undefined, b: RewardPoolConfig | PlainMessage<RewardPoolConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindRewardPoolConfigsResp
 */
export declare class FindRewardPoolConfigsResp extends Message<FindRewardPoolConfigsResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.RewardPoolConfig configs = 1;
   */
  configs: RewardPoolConfig[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindRewardPoolConfigsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindRewardPoolConfigsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindRewardPoolConfigsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindRewardPoolConfigsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindRewardPoolConfigsResp;

  static equals(a: FindRewardPoolConfigsResp | PlainMessage<FindRewardPoolConfigsResp> | undefined, b: FindRewardPoolConfigsResp | PlainMessage<FindRewardPoolConfigsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetRewardPoolConfigReq
 */
export declare class GetRewardPoolConfigReq extends Message<GetRewardPoolConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  constructor(data?: PartialMessage<GetRewardPoolConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetRewardPoolConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRewardPoolConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRewardPoolConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRewardPoolConfigReq;

  static equals(a: GetRewardPoolConfigReq | PlainMessage<GetRewardPoolConfigReq> | undefined, b: GetRewardPoolConfigReq | PlainMessage<GetRewardPoolConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetRewardPoolConfigResp
 */
export declare class GetRewardPoolConfigResp extends Message<GetRewardPoolConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.RewardPoolConfig config = 1;
   */
  config?: RewardPoolConfig;

  constructor(data?: PartialMessage<GetRewardPoolConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetRewardPoolConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRewardPoolConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRewardPoolConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRewardPoolConfigResp;

  static equals(a: GetRewardPoolConfigResp | PlainMessage<GetRewardPoolConfigResp> | undefined, b: GetRewardPoolConfigResp | PlainMessage<GetRewardPoolConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateRewardPoolConfigReq
 */
export declare class CreateRewardPoolConfigReq extends Message<CreateRewardPoolConfigReq> {
  /**
   * @generated from field: step.raccoon.bonus.RewardPoolConfig config = 1;
   */
  config?: RewardPoolConfig;

  constructor(data?: PartialMessage<CreateRewardPoolConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateRewardPoolConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateRewardPoolConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateRewardPoolConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateRewardPoolConfigReq;

  static equals(a: CreateRewardPoolConfigReq | PlainMessage<CreateRewardPoolConfigReq> | undefined, b: CreateRewardPoolConfigReq | PlainMessage<CreateRewardPoolConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateRewardPoolConfigResp
 */
export declare class CreateRewardPoolConfigResp extends Message<CreateRewardPoolConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.RewardPoolConfig config = 1;
   */
  config?: RewardPoolConfig;

  constructor(data?: PartialMessage<CreateRewardPoolConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateRewardPoolConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateRewardPoolConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateRewardPoolConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateRewardPoolConfigResp;

  static equals(a: CreateRewardPoolConfigResp | PlainMessage<CreateRewardPoolConfigResp> | undefined, b: CreateRewardPoolConfigResp | PlainMessage<CreateRewardPoolConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateRewardPoolConfigReq
 */
export declare class UpdateRewardPoolConfigReq extends Message<UpdateRewardPoolConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 2;
   */
  state?: ConfigState;

  /**
   * @generated from field: optional string name = 3;
   */
  name?: string;

  /**
   * @generated from field: optional string description = 4;
   */
  description?: string;

  /**
   * @generated from field: optional step.raccoon.bonus.RewardPool reward_pool = 11;
   */
  rewardPool?: RewardPool;

  constructor(data?: PartialMessage<UpdateRewardPoolConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateRewardPoolConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRewardPoolConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRewardPoolConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRewardPoolConfigReq;

  static equals(a: UpdateRewardPoolConfigReq | PlainMessage<UpdateRewardPoolConfigReq> | undefined, b: UpdateRewardPoolConfigReq | PlainMessage<UpdateRewardPoolConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateRewardPoolConfigResp
 */
export declare class UpdateRewardPoolConfigResp extends Message<UpdateRewardPoolConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.RewardPoolConfig config = 1;
   */
  config?: RewardPoolConfig;

  constructor(data?: PartialMessage<UpdateRewardPoolConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateRewardPoolConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRewardPoolConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRewardPoolConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRewardPoolConfigResp;

  static equals(a: UpdateRewardPoolConfigResp | PlainMessage<UpdateRewardPoolConfigResp> | undefined, b: UpdateRewardPoolConfigResp | PlainMessage<UpdateRewardPoolConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindStockItemConfigsReq
 */
export declare class FindStockItemConfigsReq extends Message<FindStockItemConfigsReq> {
  /**
   * @generated from field: step.raccoon.bonus.FindStockItemConfigsReq.FindStockItemsParam search = 1;
   */
  search?: FindStockItemConfigsReq_FindStockItemsParam;

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindStockItemConfigsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindStockItemConfigsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindStockItemConfigsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindStockItemConfigsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindStockItemConfigsReq;

  static equals(a: FindStockItemConfigsReq | PlainMessage<FindStockItemConfigsReq> | undefined, b: FindStockItemConfigsReq | PlainMessage<FindStockItemConfigsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindStockItemConfigsReq.FindStockItemsParam
 */
export declare class FindStockItemConfigsReq_FindStockItemsParam extends Message<FindStockItemConfigsReq_FindStockItemsParam> {
  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 1;
   */
  state?: ConfigState;

  constructor(data?: PartialMessage<FindStockItemConfigsReq_FindStockItemsParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindStockItemConfigsReq.FindStockItemsParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindStockItemConfigsReq_FindStockItemsParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindStockItemConfigsReq_FindStockItemsParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindStockItemConfigsReq_FindStockItemsParam;

  static equals(a: FindStockItemConfigsReq_FindStockItemsParam | PlainMessage<FindStockItemConfigsReq_FindStockItemsParam> | undefined, b: FindStockItemConfigsReq_FindStockItemsParam | PlainMessage<FindStockItemConfigsReq_FindStockItemsParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.StockItemConfig
 */
export declare class StockItemConfig extends Message<StockItemConfig> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: step.raccoon.bonus.ConfigState state = 2;
   */
  state: ConfigState;

  /**
   * @generated from field: uint32 stock = 11;
   */
  stock: number;

  /**
   * @generated from field: string name = 12;
   */
  name: string;

  /**
   * @generated from field: string updater = 101;
   */
  updater: string;

  /**
   * @generated from field: string created_at = 111;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 112;
   */
  updatedAt: string;

  constructor(data?: PartialMessage<StockItemConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.StockItemConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StockItemConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StockItemConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StockItemConfig;

  static equals(a: StockItemConfig | PlainMessage<StockItemConfig> | undefined, b: StockItemConfig | PlainMessage<StockItemConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindStockItemConfigsResp
 */
export declare class FindStockItemConfigsResp extends Message<FindStockItemConfigsResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.StockItemConfig configs = 1;
   */
  configs: StockItemConfig[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindStockItemConfigsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindStockItemConfigsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindStockItemConfigsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindStockItemConfigsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindStockItemConfigsResp;

  static equals(a: FindStockItemConfigsResp | PlainMessage<FindStockItemConfigsResp> | undefined, b: FindStockItemConfigsResp | PlainMessage<FindStockItemConfigsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetStockItemConfigReq
 */
export declare class GetStockItemConfigReq extends Message<GetStockItemConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  constructor(data?: PartialMessage<GetStockItemConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetStockItemConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetStockItemConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetStockItemConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetStockItemConfigReq;

  static equals(a: GetStockItemConfigReq | PlainMessage<GetStockItemConfigReq> | undefined, b: GetStockItemConfigReq | PlainMessage<GetStockItemConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetStockItemConfigResp
 */
export declare class GetStockItemConfigResp extends Message<GetStockItemConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.StockItemConfig config = 1;
   */
  config?: StockItemConfig;

  constructor(data?: PartialMessage<GetStockItemConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetStockItemConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetStockItemConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetStockItemConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetStockItemConfigResp;

  static equals(a: GetStockItemConfigResp | PlainMessage<GetStockItemConfigResp> | undefined, b: GetStockItemConfigResp | PlainMessage<GetStockItemConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateStockItemConfigReq
 */
export declare class CreateStockItemConfigReq extends Message<CreateStockItemConfigReq> {
  /**
   * @generated from field: step.raccoon.bonus.StockItemConfig config = 1;
   */
  config?: StockItemConfig;

  constructor(data?: PartialMessage<CreateStockItemConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateStockItemConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateStockItemConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateStockItemConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateStockItemConfigReq;

  static equals(a: CreateStockItemConfigReq | PlainMessage<CreateStockItemConfigReq> | undefined, b: CreateStockItemConfigReq | PlainMessage<CreateStockItemConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateStockItemConfigResp
 */
export declare class CreateStockItemConfigResp extends Message<CreateStockItemConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.StockItemConfig config = 1;
   */
  config?: StockItemConfig;

  constructor(data?: PartialMessage<CreateStockItemConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateStockItemConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateStockItemConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateStockItemConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateStockItemConfigResp;

  static equals(a: CreateStockItemConfigResp | PlainMessage<CreateStockItemConfigResp> | undefined, b: CreateStockItemConfigResp | PlainMessage<CreateStockItemConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateStockItemConfigReq
 */
export declare class UpdateStockItemConfigReq extends Message<UpdateStockItemConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 2;
   */
  state?: ConfigState;

  /**
   * @generated from field: optional uint32 stock = 11;
   */
  stock?: number;

  /**
   * @generated from field: optional string name = 12;
   */
  name?: string;

  constructor(data?: PartialMessage<UpdateStockItemConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateStockItemConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateStockItemConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateStockItemConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateStockItemConfigReq;

  static equals(a: UpdateStockItemConfigReq | PlainMessage<UpdateStockItemConfigReq> | undefined, b: UpdateStockItemConfigReq | PlainMessage<UpdateStockItemConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateStockItemConfigResp
 */
export declare class UpdateStockItemConfigResp extends Message<UpdateStockItemConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.StockItemConfig config = 1;
   */
  config?: StockItemConfig;

  constructor(data?: PartialMessage<UpdateStockItemConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateStockItemConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateStockItemConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateStockItemConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateStockItemConfigResp;

  static equals(a: UpdateStockItemConfigResp | PlainMessage<UpdateStockItemConfigResp> | undefined, b: UpdateStockItemConfigResp | PlainMessage<UpdateStockItemConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindBehaviorConfigsReq
 */
export declare class FindBehaviorConfigsReq extends Message<FindBehaviorConfigsReq> {
  /**
   * @generated from field: step.raccoon.bonus.FindBehaviorConfigsReq.FindBehaviorsParam search = 1;
   */
  search?: FindBehaviorConfigsReq_FindBehaviorsParam;

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindBehaviorConfigsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindBehaviorConfigsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindBehaviorConfigsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindBehaviorConfigsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindBehaviorConfigsReq;

  static equals(a: FindBehaviorConfigsReq | PlainMessage<FindBehaviorConfigsReq> | undefined, b: FindBehaviorConfigsReq | PlainMessage<FindBehaviorConfigsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindBehaviorConfigsReq.FindBehaviorsParam
 */
export declare class FindBehaviorConfigsReq_FindBehaviorsParam extends Message<FindBehaviorConfigsReq_FindBehaviorsParam> {
  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 1;
   */
  state?: ConfigState;

  constructor(data?: PartialMessage<FindBehaviorConfigsReq_FindBehaviorsParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindBehaviorConfigsReq.FindBehaviorsParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindBehaviorConfigsReq_FindBehaviorsParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindBehaviorConfigsReq_FindBehaviorsParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindBehaviorConfigsReq_FindBehaviorsParam;

  static equals(a: FindBehaviorConfigsReq_FindBehaviorsParam | PlainMessage<FindBehaviorConfigsReq_FindBehaviorsParam> | undefined, b: FindBehaviorConfigsReq_FindBehaviorsParam | PlainMessage<FindBehaviorConfigsReq_FindBehaviorsParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.BehaviorFilter
 */
export declare class BehaviorFilter extends Message<BehaviorFilter> {
  /**
   * @generated from field: uint32 DailyTimesLimit = 1;
   */
  DailyTimesLimit: number;

  /**
   * @generated from field: uint32 ContentLenAtLeast = 11;
   */
  ContentLenAtLeast: number;

  constructor(data?: PartialMessage<BehaviorFilter>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.BehaviorFilter";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BehaviorFilter;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BehaviorFilter;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BehaviorFilter;

  static equals(a: BehaviorFilter | PlainMessage<BehaviorFilter> | undefined, b: BehaviorFilter | PlainMessage<BehaviorFilter> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.BehaviorConfig
 */
export declare class BehaviorConfig extends Message<BehaviorConfig> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: step.raccoon.bonus.ConfigState state = 2;
   */
  state: ConfigState;

  /**
   * @generated from field: step.raccoon.bonus.BehaviorType behavior_type = 3;
   */
  behaviorType: BehaviorType;

  /**
   * @generated from field: step.raccoon.bonus.BehaviorFilter filter = 11;
   */
  filter?: BehaviorFilter;

  /**
   * @generated from field: string updater = 101;
   */
  updater: string;

  /**
   * @generated from field: string created_at = 111;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 112;
   */
  updatedAt: string;

  constructor(data?: PartialMessage<BehaviorConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.BehaviorConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BehaviorConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BehaviorConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BehaviorConfig;

  static equals(a: BehaviorConfig | PlainMessage<BehaviorConfig> | undefined, b: BehaviorConfig | PlainMessage<BehaviorConfig> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindBehaviorConfigsResp
 */
export declare class FindBehaviorConfigsResp extends Message<FindBehaviorConfigsResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.BehaviorConfig configs = 1;
   */
  configs: BehaviorConfig[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindBehaviorConfigsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindBehaviorConfigsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindBehaviorConfigsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindBehaviorConfigsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindBehaviorConfigsResp;

  static equals(a: FindBehaviorConfigsResp | PlainMessage<FindBehaviorConfigsResp> | undefined, b: FindBehaviorConfigsResp | PlainMessage<FindBehaviorConfigsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetBehaviorConfigReq
 */
export declare class GetBehaviorConfigReq extends Message<GetBehaviorConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  constructor(data?: PartialMessage<GetBehaviorConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetBehaviorConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBehaviorConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBehaviorConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBehaviorConfigReq;

  static equals(a: GetBehaviorConfigReq | PlainMessage<GetBehaviorConfigReq> | undefined, b: GetBehaviorConfigReq | PlainMessage<GetBehaviorConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetBehaviorConfigResp
 */
export declare class GetBehaviorConfigResp extends Message<GetBehaviorConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.BehaviorConfig config = 1;
   */
  config?: BehaviorConfig;

  constructor(data?: PartialMessage<GetBehaviorConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetBehaviorConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBehaviorConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBehaviorConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBehaviorConfigResp;

  static equals(a: GetBehaviorConfigResp | PlainMessage<GetBehaviorConfigResp> | undefined, b: GetBehaviorConfigResp | PlainMessage<GetBehaviorConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateBehaviorConfigReq
 */
export declare class CreateBehaviorConfigReq extends Message<CreateBehaviorConfigReq> {
  /**
   * @generated from field: step.raccoon.bonus.BehaviorConfig config = 1;
   */
  config?: BehaviorConfig;

  constructor(data?: PartialMessage<CreateBehaviorConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateBehaviorConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateBehaviorConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateBehaviorConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateBehaviorConfigReq;

  static equals(a: CreateBehaviorConfigReq | PlainMessage<CreateBehaviorConfigReq> | undefined, b: CreateBehaviorConfigReq | PlainMessage<CreateBehaviorConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CreateBehaviorConfigResp
 */
export declare class CreateBehaviorConfigResp extends Message<CreateBehaviorConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.BehaviorConfig config = 1;
   */
  config?: BehaviorConfig;

  constructor(data?: PartialMessage<CreateBehaviorConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CreateBehaviorConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateBehaviorConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateBehaviorConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateBehaviorConfigResp;

  static equals(a: CreateBehaviorConfigResp | PlainMessage<CreateBehaviorConfigResp> | undefined, b: CreateBehaviorConfigResp | PlainMessage<CreateBehaviorConfigResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateBehaviorConfigReq
 */
export declare class UpdateBehaviorConfigReq extends Message<UpdateBehaviorConfigReq> {
  /**
   * @generated from field: string config_id = 1;
   */
  configId: string;

  /**
   * @generated from field: optional step.raccoon.bonus.ConfigState state = 2;
   */
  state?: ConfigState;

  /**
   * @generated from field: optional step.raccoon.bonus.BehaviorType behavior_type = 3;
   */
  behaviorType?: BehaviorType;

  /**
   * @generated from field: optional step.raccoon.bonus.BehaviorFilter filter = 4;
   */
  filter?: BehaviorFilter;

  constructor(data?: PartialMessage<UpdateBehaviorConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateBehaviorConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateBehaviorConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateBehaviorConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateBehaviorConfigReq;

  static equals(a: UpdateBehaviorConfigReq | PlainMessage<UpdateBehaviorConfigReq> | undefined, b: UpdateBehaviorConfigReq | PlainMessage<UpdateBehaviorConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateBehaviorConfigResp
 */
export declare class UpdateBehaviorConfigResp extends Message<UpdateBehaviorConfigResp> {
  /**
   * @generated from field: step.raccoon.bonus.BehaviorConfig config = 1;
   */
  config?: BehaviorConfig;

  constructor(data?: PartialMessage<UpdateBehaviorConfigResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateBehaviorConfigResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateBehaviorConfigResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateBehaviorConfigResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateBehaviorConfigResp;

  static equals(a: UpdateBehaviorConfigResp | PlainMessage<UpdateBehaviorConfigResp> | undefined, b: UpdateBehaviorConfigResp | PlainMessage<UpdateBehaviorConfigResp> | undefined): boolean;
}

