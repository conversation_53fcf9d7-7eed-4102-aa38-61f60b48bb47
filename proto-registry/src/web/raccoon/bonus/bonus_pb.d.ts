// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bonus/bonus.proto (package step.raccoon.bonus, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { DrawCheckInTargetType, ItemType, NormalTargetType, RecordType } from "./common_pb.js";
import type { RewardType } from "../common/types_pb.js";
import type { Pagination } from "../common/utils_pb.js";

/**
 * @generated from message step.raccoon.bonus.TaskDonePush
 */
export declare class TaskDonePush extends Message<TaskDonePush> {
  /**
   * @generated from field: string toast = 1;
   */
  toast: string;

  /**
   * @generated from field: step.raccoon.bonus.NormalTargetType target_type = 101;
   */
  targetType: NormalTargetType;

  constructor(data?: PartialMessage<TaskDonePush>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.TaskDonePush";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TaskDonePush;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TaskDonePush;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TaskDonePush;

  static equals(a: TaskDonePush | PlainMessage<TaskDonePush> | undefined, b: TaskDonePush | PlainMessage<TaskDonePush> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.Reward
 */
export declare class Reward extends Message<Reward> {
  /**
   * @generated from field: string image_url = 1;
   */
  imageUrl: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: step.raccoon.common.RewardType reward_type = 11;
   */
  rewardType: RewardType;

  /**
   * @generated from field: uint32 count = 12;
   */
  count: number;

  constructor(data?: PartialMessage<Reward>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.Reward";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Reward;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Reward;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Reward;

  static equals(a: Reward | PlainMessage<Reward> | undefined, b: Reward | PlainMessage<Reward> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindNormalCheckInTaskReq
 */
export declare class FindNormalCheckInTaskReq extends Message<FindNormalCheckInTaskReq> {
  constructor(data?: PartialMessage<FindNormalCheckInTaskReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindNormalCheckInTaskReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindNormalCheckInTaskReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindNormalCheckInTaskReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindNormalCheckInTaskReq;

  static equals(a: FindNormalCheckInTaskReq | PlainMessage<FindNormalCheckInTaskReq> | undefined, b: FindNormalCheckInTaskReq | PlainMessage<FindNormalCheckInTaskReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.NormalCheckInTask
 */
export declare class NormalCheckInTask extends Message<NormalCheckInTask> {
  /**
   * @generated from field: string task_key = 1;
   */
  taskKey: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string start_at = 4;
   */
  startAt: string;

  /**
   * @generated from field: string end_at = 5;
   */
  endAt: string;

  /**
   * @generated from field: repeated step.raccoon.bonus.NormalCheckInTask.Target targets = 11;
   */
  targets: NormalCheckInTask_Target[];

  /**
   * @generated from field: repeated step.raccoon.bonus.NormalCheckInTask.TargetProcess processes = 101;
   */
  processes: NormalCheckInTask_TargetProcess[];

  constructor(data?: PartialMessage<NormalCheckInTask>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.NormalCheckInTask";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NormalCheckInTask;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NormalCheckInTask;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NormalCheckInTask;

  static equals(a: NormalCheckInTask | PlainMessage<NormalCheckInTask> | undefined, b: NormalCheckInTask | PlainMessage<NormalCheckInTask> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.NormalCheckInTask.Target
 */
export declare class NormalCheckInTask_Target extends Message<NormalCheckInTask_Target> {
  /**
   * @generated from field: step.raccoon.bonus.Reward reward = 1;
   */
  reward?: Reward;

  constructor(data?: PartialMessage<NormalCheckInTask_Target>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.NormalCheckInTask.Target";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NormalCheckInTask_Target;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NormalCheckInTask_Target;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NormalCheckInTask_Target;

  static equals(a: NormalCheckInTask_Target | PlainMessage<NormalCheckInTask_Target> | undefined, b: NormalCheckInTask_Target | PlainMessage<NormalCheckInTask_Target> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.NormalCheckInTask.TargetProcess
 */
export declare class NormalCheckInTask_TargetProcess extends Message<NormalCheckInTask_TargetProcess> {
  /**
   * @generated from field: string done_at = 1;
   */
  doneAt: string;

  constructor(data?: PartialMessage<NormalCheckInTask_TargetProcess>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.NormalCheckInTask.TargetProcess";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NormalCheckInTask_TargetProcess;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NormalCheckInTask_TargetProcess;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NormalCheckInTask_TargetProcess;

  static equals(a: NormalCheckInTask_TargetProcess | PlainMessage<NormalCheckInTask_TargetProcess> | undefined, b: NormalCheckInTask_TargetProcess | PlainMessage<NormalCheckInTask_TargetProcess> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindNormalCheckInTaskResp
 */
export declare class FindNormalCheckInTaskResp extends Message<FindNormalCheckInTaskResp> {
  /**
   * @generated from field: step.raccoon.bonus.NormalCheckInTask task = 1;
   */
  task?: NormalCheckInTask;

  constructor(data?: PartialMessage<FindNormalCheckInTaskResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindNormalCheckInTaskResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindNormalCheckInTaskResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindNormalCheckInTaskResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindNormalCheckInTaskResp;

  static equals(a: FindNormalCheckInTaskResp | PlainMessage<FindNormalCheckInTaskResp> | undefined, b: FindNormalCheckInTaskResp | PlainMessage<FindNormalCheckInTaskResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindCheckInInfosReq
 */
export declare class FindCheckInInfosReq extends Message<FindCheckInInfosReq> {
  /**
   * @generated from field: string start_at = 1;
   */
  startAt: string;

  /**
   * @generated from field: string end_at = 2;
   */
  endAt: string;

  constructor(data?: PartialMessage<FindCheckInInfosReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindCheckInInfosReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindCheckInInfosReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindCheckInInfosReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindCheckInInfosReq;

  static equals(a: FindCheckInInfosReq | PlainMessage<FindCheckInInfosReq> | undefined, b: FindCheckInInfosReq | PlainMessage<FindCheckInInfosReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInInfo
 */
export declare class CheckInInfo extends Message<CheckInInfo> {
  /**
   * @generated from field: string checked_in_at = 1;
   */
  checkedInAt: string;

  constructor(data?: PartialMessage<CheckInInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInInfo;

  static equals(a: CheckInInfo | PlainMessage<CheckInInfo> | undefined, b: CheckInInfo | PlainMessage<CheckInInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindCheckInInfosResp
 */
export declare class FindCheckInInfosResp extends Message<FindCheckInInfosResp> {
  /**
   * @generated from field: uint32 consecutive_count = 1;
   */
  consecutiveCount: number;

  /**
   * @generated from field: repeated step.raccoon.bonus.CheckInInfo check_in_infos = 2;
   */
  checkInInfos: CheckInInfo[];

  constructor(data?: PartialMessage<FindCheckInInfosResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindCheckInInfosResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindCheckInInfosResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindCheckInInfosResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindCheckInInfosResp;

  static equals(a: FindCheckInInfosResp | PlainMessage<FindCheckInInfosResp> | undefined, b: FindCheckInInfosResp | PlainMessage<FindCheckInInfosResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInReq
 */
export declare class CheckInReq extends Message<CheckInReq> {
  constructor(data?: PartialMessage<CheckInReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInReq;

  static equals(a: CheckInReq | PlainMessage<CheckInReq> | undefined, b: CheckInReq | PlainMessage<CheckInReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInResp
 */
export declare class CheckInResp extends Message<CheckInResp> {
  /**
   * @generated from field: bool done = 1;
   */
  done: boolean;

  constructor(data?: PartialMessage<CheckInResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInResp;

  static equals(a: CheckInResp | PlainMessage<CheckInResp> | undefined, b: CheckInResp | PlainMessage<CheckInResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetOtherCheckInTaskRemindReq
 */
export declare class GetOtherCheckInTaskRemindReq extends Message<GetOtherCheckInTaskRemindReq> {
  constructor(data?: PartialMessage<GetOtherCheckInTaskRemindReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetOtherCheckInTaskRemindReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetOtherCheckInTaskRemindReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetOtherCheckInTaskRemindReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetOtherCheckInTaskRemindReq;

  static equals(a: GetOtherCheckInTaskRemindReq | PlainMessage<GetOtherCheckInTaskRemindReq> | undefined, b: GetOtherCheckInTaskRemindReq | PlainMessage<GetOtherCheckInTaskRemindReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetOtherCheckInTaskRemindResp
 */
export declare class GetOtherCheckInTaskRemindResp extends Message<GetOtherCheckInTaskRemindResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.GetOtherCheckInTaskRemindResp.TargetProcess processes = 1;
   */
  processes: GetOtherCheckInTaskRemindResp_TargetProcess[];

  constructor(data?: PartialMessage<GetOtherCheckInTaskRemindResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetOtherCheckInTaskRemindResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetOtherCheckInTaskRemindResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetOtherCheckInTaskRemindResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetOtherCheckInTaskRemindResp;

  static equals(a: GetOtherCheckInTaskRemindResp | PlainMessage<GetOtherCheckInTaskRemindResp> | undefined, b: GetOtherCheckInTaskRemindResp | PlainMessage<GetOtherCheckInTaskRemindResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetOtherCheckInTaskRemindResp.TargetProcess
 */
export declare class GetOtherCheckInTaskRemindResp_TargetProcess extends Message<GetOtherCheckInTaskRemindResp_TargetProcess> {
  /**
   * @generated from field: repeated string task_keys = 1;
   */
  taskKeys: string[];

  constructor(data?: PartialMessage<GetOtherCheckInTaskRemindResp_TargetProcess>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetOtherCheckInTaskRemindResp.TargetProcess";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetOtherCheckInTaskRemindResp_TargetProcess;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetOtherCheckInTaskRemindResp_TargetProcess;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetOtherCheckInTaskRemindResp_TargetProcess;

  static equals(a: GetOtherCheckInTaskRemindResp_TargetProcess | PlainMessage<GetOtherCheckInTaskRemindResp_TargetProcess> | undefined, b: GetOtherCheckInTaskRemindResp_TargetProcess | PlainMessage<GetOtherCheckInTaskRemindResp_TargetProcess> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.DailyTarotRecord
 */
export declare class DailyTarotRecord extends Message<DailyTarotRecord> {
  /**
   * @generated from field: string divined_at = 1;
   */
  divinedAt: string;

  /**
   * @generated from field: string arcana_name = 11;
   */
  arcanaName: string;

  /**
   * @generated from field: string image_url = 12;
   */
  imageUrl: string;

  /**
   * @generated from field: string description = 13;
   */
  description: string;

  constructor(data?: PartialMessage<DailyTarotRecord>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.DailyTarotRecord";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DailyTarotRecord;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DailyTarotRecord;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DailyTarotRecord;

  static equals(a: DailyTarotRecord | PlainMessage<DailyTarotRecord> | undefined, b: DailyTarotRecord | PlainMessage<DailyTarotRecord> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetDailyTarotReq
 */
export declare class GetDailyTarotReq extends Message<GetDailyTarotReq> {
  constructor(data?: PartialMessage<GetDailyTarotReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetDailyTarotReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDailyTarotReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDailyTarotReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDailyTarotReq;

  static equals(a: GetDailyTarotReq | PlainMessage<GetDailyTarotReq> | undefined, b: GetDailyTarotReq | PlainMessage<GetDailyTarotReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetDailyTarotResp
 */
export declare class GetDailyTarotResp extends Message<GetDailyTarotResp> {
  /**
   * @generated from field: step.raccoon.bonus.DailyTarotRecord daily_tarot_record = 1;
   */
  dailyTarotRecord?: DailyTarotRecord;

  constructor(data?: PartialMessage<GetDailyTarotResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetDailyTarotResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDailyTarotResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDailyTarotResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDailyTarotResp;

  static equals(a: GetDailyTarotResp | PlainMessage<GetDailyTarotResp> | undefined, b: GetDailyTarotResp | PlainMessage<GetDailyTarotResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetDailyTarotHistoryReq
 */
export declare class GetDailyTarotHistoryReq extends Message<GetDailyTarotHistoryReq> {
  /**
   * @generated from field: string start_at = 1;
   */
  startAt: string;

  /**
   * @generated from field: string end_at = 2;
   */
  endAt: string;

  constructor(data?: PartialMessage<GetDailyTarotHistoryReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetDailyTarotHistoryReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDailyTarotHistoryReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDailyTarotHistoryReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDailyTarotHistoryReq;

  static equals(a: GetDailyTarotHistoryReq | PlainMessage<GetDailyTarotHistoryReq> | undefined, b: GetDailyTarotHistoryReq | PlainMessage<GetDailyTarotHistoryReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetDailyTarotHistoryResp
 */
export declare class GetDailyTarotHistoryResp extends Message<GetDailyTarotHistoryResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.DailyTarotRecord daily_tarot_records = 1;
   */
  dailyTarotRecords: DailyTarotRecord[];

  constructor(data?: PartialMessage<GetDailyTarotHistoryResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetDailyTarotHistoryResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDailyTarotHistoryResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDailyTarotHistoryResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDailyTarotHistoryResp;

  static equals(a: GetDailyTarotHistoryResp | PlainMessage<GetDailyTarotHistoryResp> | undefined, b: GetDailyTarotHistoryResp | PlainMessage<GetDailyTarotHistoryResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindGroupTaskReq
 */
export declare class FindGroupTaskReq extends Message<FindGroupTaskReq> {
  constructor(data?: PartialMessage<FindGroupTaskReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindGroupTaskReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindGroupTaskReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindGroupTaskReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindGroupTaskReq;

  static equals(a: FindGroupTaskReq | PlainMessage<FindGroupTaskReq> | undefined, b: FindGroupTaskReq | PlainMessage<FindGroupTaskReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GroupTask
 */
export declare class GroupTask extends Message<GroupTask> {
  /**
   * @generated from field: string task_key = 1;
   */
  taskKey: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string start_at = 4;
   */
  startAt: string;

  /**
   * @generated from field: string end_at = 5;
   */
  endAt: string;

  /**
   * @generated from field: repeated step.raccoon.bonus.GroupTask.Target targets = 11;
   */
  targets: GroupTask_Target[];

  /**
   * @generated from field: repeated step.raccoon.bonus.GroupTask.TargetProcess processes = 21;
   */
  processes: GroupTask_TargetProcess[];

  /**
   * @generated from field: uint32 total_target_count = 101;
   */
  totalTargetCount: number;

  /**
   * @generated from field: uint32 total_process_count = 102;
   */
  totalProcessCount: number;

  constructor(data?: PartialMessage<GroupTask>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GroupTask";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GroupTask;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GroupTask;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GroupTask;

  static equals(a: GroupTask | PlainMessage<GroupTask> | undefined, b: GroupTask | PlainMessage<GroupTask> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GroupTask.Target
 */
export declare class GroupTask_Target extends Message<GroupTask_Target> {
  /**
   * @generated from field: step.raccoon.bonus.Reward reward = 1;
   */
  reward?: Reward;

  /**
   * @generated from field: uint32 target_count = 11;
   */
  targetCount: number;

  constructor(data?: PartialMessage<GroupTask_Target>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GroupTask.Target";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GroupTask_Target;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GroupTask_Target;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GroupTask_Target;

  static equals(a: GroupTask_Target | PlainMessage<GroupTask_Target> | undefined, b: GroupTask_Target | PlainMessage<GroupTask_Target> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GroupTask.TargetProcess
 */
export declare class GroupTask_TargetProcess extends Message<GroupTask_TargetProcess> {
  /**
   * @generated from field: string done_at = 1;
   */
  doneAt: string;

  /**
   * @generated from field: uint32 count = 11;
   */
  count: number;

  constructor(data?: PartialMessage<GroupTask_TargetProcess>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GroupTask.TargetProcess";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GroupTask_TargetProcess;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GroupTask_TargetProcess;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GroupTask_TargetProcess;

  static equals(a: GroupTask_TargetProcess | PlainMessage<GroupTask_TargetProcess> | undefined, b: GroupTask_TargetProcess | PlainMessage<GroupTask_TargetProcess> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindGroupTaskResp
 */
export declare class FindGroupTaskResp extends Message<FindGroupTaskResp> {
  /**
   * @generated from field: step.raccoon.bonus.GroupTask task = 1;
   */
  task?: GroupTask;

  constructor(data?: PartialMessage<FindGroupTaskResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindGroupTaskResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindGroupTaskResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindGroupTaskResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindGroupTaskResp;

  static equals(a: FindGroupTaskResp | PlainMessage<FindGroupTaskResp> | undefined, b: FindGroupTaskResp | PlainMessage<FindGroupTaskResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindNormalTasksReq
 */
export declare class FindNormalTasksReq extends Message<FindNormalTasksReq> {
  constructor(data?: PartialMessage<FindNormalTasksReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindNormalTasksReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindNormalTasksReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindNormalTasksReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindNormalTasksReq;

  static equals(a: FindNormalTasksReq | PlainMessage<FindNormalTasksReq> | undefined, b: FindNormalTasksReq | PlainMessage<FindNormalTasksReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.NormalTask
 */
export declare class NormalTask extends Message<NormalTask> {
  /**
   * @generated from field: string task_key = 1;
   */
  taskKey: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string start_at = 4;
   */
  startAt: string;

  /**
   * @generated from field: string end_at = 5;
   */
  endAt: string;

  /**
   * @generated from field: repeated step.raccoon.bonus.NormalTask.Target targets = 11;
   */
  targets: NormalTask_Target[];

  /**
   * @generated from field: string redirect_link = 12;
   */
  redirectLink: string;

  /**
   * @generated from field: repeated step.raccoon.bonus.NormalTask.TargetProcess processes = 21;
   */
  processes: NormalTask_TargetProcess[];

  constructor(data?: PartialMessage<NormalTask>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.NormalTask";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NormalTask;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NormalTask;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NormalTask;

  static equals(a: NormalTask | PlainMessage<NormalTask> | undefined, b: NormalTask | PlainMessage<NormalTask> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.NormalTask.Target
 */
export declare class NormalTask_Target extends Message<NormalTask_Target> {
  /**
   * @generated from field: step.raccoon.bonus.Reward reward = 1;
   */
  reward?: Reward;

  /**
   * @generated from field: uint32 target_count = 11;
   */
  targetCount: number;

  /**
   * @generated from field: step.raccoon.bonus.NormalTargetType target_type = 101;
   */
  targetType: NormalTargetType;

  constructor(data?: PartialMessage<NormalTask_Target>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.NormalTask.Target";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NormalTask_Target;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NormalTask_Target;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NormalTask_Target;

  static equals(a: NormalTask_Target | PlainMessage<NormalTask_Target> | undefined, b: NormalTask_Target | PlainMessage<NormalTask_Target> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.NormalTask.TargetProcess
 */
export declare class NormalTask_TargetProcess extends Message<NormalTask_TargetProcess> {
  /**
   * @generated from field: string done_at = 1;
   */
  doneAt: string;

  /**
   * @generated from field: uint32 count = 11;
   */
  count: number;

  constructor(data?: PartialMessage<NormalTask_TargetProcess>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.NormalTask.TargetProcess";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NormalTask_TargetProcess;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NormalTask_TargetProcess;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NormalTask_TargetProcess;

  static equals(a: NormalTask_TargetProcess | PlainMessage<NormalTask_TargetProcess> | undefined, b: NormalTask_TargetProcess | PlainMessage<NormalTask_TargetProcess> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindNormalTasksResp
 */
export declare class FindNormalTasksResp extends Message<FindNormalTasksResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.NormalTask tasks = 1;
   */
  tasks: NormalTask[];

  constructor(data?: PartialMessage<FindNormalTasksResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindNormalTasksResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindNormalTasksResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindNormalTasksResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindNormalTasksResp;

  static equals(a: FindNormalTasksResp | PlainMessage<FindNormalTasksResp> | undefined, b: FindNormalTasksResp | PlainMessage<FindNormalTasksResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetTaskRewardReq
 */
export declare class GetTaskRewardReq extends Message<GetTaskRewardReq> {
  /**
   * @generated from field: string task_key = 1;
   */
  taskKey: string;

  /**
   * @generated from field: uint32 target_index = 2;
   */
  targetIndex: number;

  constructor(data?: PartialMessage<GetTaskRewardReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetTaskRewardReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTaskRewardReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTaskRewardReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTaskRewardReq;

  static equals(a: GetTaskRewardReq | PlainMessage<GetTaskRewardReq> | undefined, b: GetTaskRewardReq | PlainMessage<GetTaskRewardReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetTaskRewardResp
 */
export declare class GetTaskRewardResp extends Message<GetTaskRewardResp> {
  /**
   * @generated from field: bool done = 1;
   */
  done: boolean;

  constructor(data?: PartialMessage<GetTaskRewardResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetTaskRewardResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTaskRewardResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTaskRewardResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTaskRewardResp;

  static equals(a: GetTaskRewardResp | PlainMessage<GetTaskRewardResp> | undefined, b: GetTaskRewardResp | PlainMessage<GetTaskRewardResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInRemindReq
 */
export declare class CheckInRemindReq extends Message<CheckInRemindReq> {
  /**
   * @generated from field: optional bool turn_on = 1;
   */
  turnOn?: boolean;

  constructor(data?: PartialMessage<CheckInRemindReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInRemindReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInRemindReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInRemindReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInRemindReq;

  static equals(a: CheckInRemindReq | PlainMessage<CheckInRemindReq> | undefined, b: CheckInRemindReq | PlainMessage<CheckInRemindReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.CheckInRemindResp
 */
export declare class CheckInRemindResp extends Message<CheckInRemindResp> {
  /**
   * @generated from field: bool on = 1;
   */
  on: boolean;

  constructor(data?: PartialMessage<CheckInRemindResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInRemindResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInRemindResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInRemindResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInRemindResp;

  static equals(a: CheckInRemindResp | PlainMessage<CheckInRemindResp> | undefined, b: CheckInRemindResp | PlainMessage<CheckInRemindResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindRecordsReq
 */
export declare class FindRecordsReq extends Message<FindRecordsReq> {
  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindRecordsReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindRecordsReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindRecordsReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindRecordsReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindRecordsReq;

  static equals(a: FindRecordsReq | PlainMessage<FindRecordsReq> | undefined, b: FindRecordsReq | PlainMessage<FindRecordsReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.Record
 */
export declare class Record extends Message<Record> {
  /**
   * @generated from field: step.raccoon.bonus.RecordType record_type = 1;
   */
  recordType: RecordType;

  /**
   * @generated from field: string record_id = 2;
   */
  recordId: string;

  /**
   * @generated from field: step.raccoon.bonus.Reward reward = 11;
   */
  reward?: Reward;

  /**
   * @generated from field: step.raccoon.bonus.ShippingInfo shipping_info = 12;
   */
  shippingInfo?: ShippingInfo;

  /**
   * @generated from field: string recorded_at = 101;
   */
  recordedAt: string;

  constructor(data?: PartialMessage<Record>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.Record";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Record;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Record;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Record;

  static equals(a: Record | PlainMessage<Record> | undefined, b: Record | PlainMessage<Record> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindRecordsResp
 */
export declare class FindRecordsResp extends Message<FindRecordsResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.Record records = 1;
   */
  records: Record[];

  /**
   * @generated from field: step.raccoon.common.Pagination pagination = 101;
   */
  pagination?: Pagination;

  constructor(data?: PartialMessage<FindRecordsResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindRecordsResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindRecordsResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindRecordsResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindRecordsResp;

  static equals(a: FindRecordsResp | PlainMessage<FindRecordsResp> | undefined, b: FindRecordsResp | PlainMessage<FindRecordsResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetRewardPoolReq
 */
export declare class GetRewardPoolReq extends Message<GetRewardPoolReq> {
  constructor(data?: PartialMessage<GetRewardPoolReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetRewardPoolReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRewardPoolReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRewardPoolReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRewardPoolReq;

  static equals(a: GetRewardPoolReq | PlainMessage<GetRewardPoolReq> | undefined, b: GetRewardPoolReq | PlainMessage<GetRewardPoolReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetRewardPoolResp
 */
export declare class GetRewardPoolResp extends Message<GetRewardPoolResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.Reward rewards = 1;
   */
  rewards: Reward[];

  constructor(data?: PartialMessage<GetRewardPoolResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetRewardPoolResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRewardPoolResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRewardPoolResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRewardPoolResp;

  static equals(a: GetRewardPoolResp | PlainMessage<GetRewardPoolResp> | undefined, b: GetRewardPoolResp | PlainMessage<GetRewardPoolResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindDrawCheckInTasksReq
 */
export declare class FindDrawCheckInTasksReq extends Message<FindDrawCheckInTasksReq> {
  constructor(data?: PartialMessage<FindDrawCheckInTasksReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindDrawCheckInTasksReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindDrawCheckInTasksReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindDrawCheckInTasksReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindDrawCheckInTasksReq;

  static equals(a: FindDrawCheckInTasksReq | PlainMessage<FindDrawCheckInTasksReq> | undefined, b: FindDrawCheckInTasksReq | PlainMessage<FindDrawCheckInTasksReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindDrawCheckInTasksResp
 */
export declare class FindDrawCheckInTasksResp extends Message<FindDrawCheckInTasksResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.DrawCheckInTask draw_tasks = 1;
   */
  drawTasks: DrawCheckInTask[];

  constructor(data?: PartialMessage<FindDrawCheckInTasksResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindDrawCheckInTasksResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindDrawCheckInTasksResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindDrawCheckInTasksResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindDrawCheckInTasksResp;

  static equals(a: FindDrawCheckInTasksResp | PlainMessage<FindDrawCheckInTasksResp> | undefined, b: FindDrawCheckInTasksResp | PlainMessage<FindDrawCheckInTasksResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.DrawCheckInTask
 */
export declare class DrawCheckInTask extends Message<DrawCheckInTask> {
  /**
   * @generated from field: string task_key = 1;
   */
  taskKey: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string start_at = 4;
   */
  startAt: string;

  /**
   * @generated from field: string end_at = 5;
   */
  endAt: string;

  /**
   * @generated from field: repeated step.raccoon.bonus.DrawCheckInTask.Target targets = 11;
   */
  targets: DrawCheckInTask_Target[];

  /**
   * @generated from field: repeated step.raccoon.bonus.DrawCheckInTask.TargetProcess processes = 21;
   */
  processes: DrawCheckInTask_TargetProcess[];

  constructor(data?: PartialMessage<DrawCheckInTask>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.DrawCheckInTask";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DrawCheckInTask;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DrawCheckInTask;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DrawCheckInTask;

  static equals(a: DrawCheckInTask | PlainMessage<DrawCheckInTask> | undefined, b: DrawCheckInTask | PlainMessage<DrawCheckInTask> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.DrawCheckInTask.Target
 */
export declare class DrawCheckInTask_Target extends Message<DrawCheckInTask_Target> {
  /**
   * @generated from field: step.raccoon.bonus.Reward reward = 1;
   */
  reward?: Reward;

  /**
   * @generated from field: uint32 target_count = 11;
   */
  targetCount: number;

  /**
   * @generated from field: uint32 fixed_date = 12;
   */
  fixedDate: number;

  /**
   * @generated from field: step.raccoon.bonus.DrawCheckInTargetType target_type = 101;
   */
  targetType: DrawCheckInTargetType;

  constructor(data?: PartialMessage<DrawCheckInTask_Target>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.DrawCheckInTask.Target";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DrawCheckInTask_Target;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DrawCheckInTask_Target;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DrawCheckInTask_Target;

  static equals(a: DrawCheckInTask_Target | PlainMessage<DrawCheckInTask_Target> | undefined, b: DrawCheckInTask_Target | PlainMessage<DrawCheckInTask_Target> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.DrawCheckInTask.TargetProcess
 */
export declare class DrawCheckInTask_TargetProcess extends Message<DrawCheckInTask_TargetProcess> {
  /**
   * @generated from field: string done_at = 1;
   */
  doneAt: string;

  /**
   * @generated from field: uint32 count = 11;
   */
  count: number;

  constructor(data?: PartialMessage<DrawCheckInTask_TargetProcess>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.DrawCheckInTask.TargetProcess";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DrawCheckInTask_TargetProcess;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DrawCheckInTask_TargetProcess;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DrawCheckInTask_TargetProcess;

  static equals(a: DrawCheckInTask_TargetProcess | PlainMessage<DrawCheckInTask_TargetProcess> | undefined, b: DrawCheckInTask_TargetProcess | PlainMessage<DrawCheckInTask_TargetProcess> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetBonusBackpackReq
 */
export declare class GetBonusBackpackReq extends Message<GetBonusBackpackReq> {
  constructor(data?: PartialMessage<GetBonusBackpackReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetBonusBackpackReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBonusBackpackReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBonusBackpackReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBonusBackpackReq;

  static equals(a: GetBonusBackpackReq | PlainMessage<GetBonusBackpackReq> | undefined, b: GetBonusBackpackReq | PlainMessage<GetBonusBackpackReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.GetBonusBackpackResp
 */
export declare class GetBonusBackpackResp extends Message<GetBonusBackpackResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.BackpackItem items = 1;
   */
  items: BackpackItem[];

  constructor(data?: PartialMessage<GetBonusBackpackResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.GetBonusBackpackResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetBonusBackpackResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetBonusBackpackResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetBonusBackpackResp;

  static equals(a: GetBonusBackpackResp | PlainMessage<GetBonusBackpackResp> | undefined, b: GetBonusBackpackResp | PlainMessage<GetBonusBackpackResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.BackpackItem
 */
export declare class BackpackItem extends Message<BackpackItem> {
  /**
   * @generated from field: step.raccoon.bonus.ItemType item_type = 1;
   */
  itemType: ItemType;

  /**
   * @generated from field: uint32 count = 11;
   */
  count: number;

  /**
   * @generated from field: string expired_at = 12;
   */
  expiredAt: string;

  constructor(data?: PartialMessage<BackpackItem>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.BackpackItem";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BackpackItem;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BackpackItem;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BackpackItem;

  static equals(a: BackpackItem | PlainMessage<BackpackItem> | undefined, b: BackpackItem | PlainMessage<BackpackItem> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.DoDrawReq
 */
export declare class DoDrawReq extends Message<DoDrawReq> {
  constructor(data?: PartialMessage<DoDrawReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.DoDrawReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DoDrawReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DoDrawReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DoDrawReq;

  static equals(a: DoDrawReq | PlainMessage<DoDrawReq> | undefined, b: DoDrawReq | PlainMessage<DoDrawReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.DoDrawResp
 */
export declare class DoDrawResp extends Message<DoDrawResp> {
  /**
   * @generated from field: step.raccoon.bonus.Reward reward = 1;
   */
  reward?: Reward;

  constructor(data?: PartialMessage<DoDrawResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.DoDrawResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DoDrawResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DoDrawResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DoDrawResp;

  static equals(a: DoDrawResp | PlainMessage<DoDrawResp> | undefined, b: DoDrawResp | PlainMessage<DoDrawResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.ShippingInfo
 */
export declare class ShippingInfo extends Message<ShippingInfo> {
  /**
   * @generated from field: string recipient_name = 1;
   */
  recipientName: string;

  /**
   * @generated from field: string phone = 2;
   */
  phone: string;

  /**
   * @generated from field: string address = 3;
   */
  address: string;

  constructor(data?: PartialMessage<ShippingInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.ShippingInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ShippingInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ShippingInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ShippingInfo;

  static equals(a: ShippingInfo | PlainMessage<ShippingInfo> | undefined, b: ShippingInfo | PlainMessage<ShippingInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateRecordReq
 */
export declare class UpdateRecordReq extends Message<UpdateRecordReq> {
  /**
   * @generated from field: string record_id = 1;
   */
  recordId: string;

  /**
   * @generated from field: optional step.raccoon.bonus.ShippingInfo shipping_info = 11;
   */
  shippingInfo?: ShippingInfo;

  constructor(data?: PartialMessage<UpdateRecordReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateRecordReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRecordReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRecordReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRecordReq;

  static equals(a: UpdateRecordReq | PlainMessage<UpdateRecordReq> | undefined, b: UpdateRecordReq | PlainMessage<UpdateRecordReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.UpdateRecordResp
 */
export declare class UpdateRecordResp extends Message<UpdateRecordResp> {
  /**
   * @generated from field: step.raccoon.bonus.Record record = 1;
   */
  record?: Record;

  constructor(data?: PartialMessage<UpdateRecordResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.UpdateRecordResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRecordResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRecordResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRecordResp;

  static equals(a: UpdateRecordResp | PlainMessage<UpdateRecordResp> | undefined, b: UpdateRecordResp | PlainMessage<UpdateRecordResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.AchievementTask
 */
export declare class AchievementTask extends Message<AchievementTask> {
  /**
   * @generated from field: string task_key = 1;
   */
  taskKey: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string start_at = 4;
   */
  startAt: string;

  /**
   * @generated from field: string end_at = 5;
   */
  endAt: string;

  /**
   * @generated from field: repeated step.raccoon.bonus.AchievementTask.Target targets = 11;
   */
  targets: AchievementTask_Target[];

  /**
   * @generated from field: repeated step.raccoon.bonus.AchievementTask.TargetProcess processes = 21;
   */
  processes: AchievementTask_TargetProcess[];

  constructor(data?: PartialMessage<AchievementTask>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.AchievementTask";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AchievementTask;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AchievementTask;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AchievementTask;

  static equals(a: AchievementTask | PlainMessage<AchievementTask> | undefined, b: AchievementTask | PlainMessage<AchievementTask> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.AchievementTask.Target
 */
export declare class AchievementTask_Target extends Message<AchievementTask_Target> {
  /**
   * @generated from field: repeated step.raccoon.bonus.Reward multi_rewards = 1;
   */
  multiRewards: Reward[];

  /**
   * @generated from field: uint32 target_count = 11;
   */
  targetCount: number;

  constructor(data?: PartialMessage<AchievementTask_Target>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.AchievementTask.Target";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AchievementTask_Target;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AchievementTask_Target;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AchievementTask_Target;

  static equals(a: AchievementTask_Target | PlainMessage<AchievementTask_Target> | undefined, b: AchievementTask_Target | PlainMessage<AchievementTask_Target> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.AchievementTask.TargetProcess
 */
export declare class AchievementTask_TargetProcess extends Message<AchievementTask_TargetProcess> {
  /**
   * @generated from field: string done_at = 1;
   */
  doneAt: string;

  /**
   * @generated from field: uint32 count = 11;
   */
  count: number;

  constructor(data?: PartialMessage<AchievementTask_TargetProcess>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.AchievementTask.TargetProcess";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AchievementTask_TargetProcess;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AchievementTask_TargetProcess;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AchievementTask_TargetProcess;

  static equals(a: AchievementTask_TargetProcess | PlainMessage<AchievementTask_TargetProcess> | undefined, b: AchievementTask_TargetProcess | PlainMessage<AchievementTask_TargetProcess> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindAchievementTasksReq
 */
export declare class FindAchievementTasksReq extends Message<FindAchievementTasksReq> {
  constructor(data?: PartialMessage<FindAchievementTasksReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindAchievementTasksReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindAchievementTasksReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindAchievementTasksReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindAchievementTasksReq;

  static equals(a: FindAchievementTasksReq | PlainMessage<FindAchievementTasksReq> | undefined, b: FindAchievementTasksReq | PlainMessage<FindAchievementTasksReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.bonus.FindAchievementTasksResp
 */
export declare class FindAchievementTasksResp extends Message<FindAchievementTasksResp> {
  /**
   * @generated from field: repeated step.raccoon.bonus.AchievementTask tasks = 1;
   */
  tasks: AchievementTask[];

  constructor(data?: PartialMessage<FindAchievementTasksResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.FindAchievementTasksResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindAchievementTasksResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindAchievementTasksResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindAchievementTasksResp;

  static equals(a: FindAchievementTasksResp | PlainMessage<FindAchievementTasksResp> | undefined, b: FindAchievementTasksResp | PlainMessage<FindAchievementTasksResp> | undefined): boolean;
}

