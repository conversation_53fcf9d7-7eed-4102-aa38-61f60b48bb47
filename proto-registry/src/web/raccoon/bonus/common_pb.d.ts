// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/bonus/common.proto (package step.raccoon.bonus, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum step.raccoon.bonus.ConfigState
 */
export declare enum ConfigState {
  /**
   * @generated from enum value: CONFIG_STATE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 有效
   *
   * @generated from enum value: CONFIG_STATE_EFFECTIVE = 1;
   */
  EFFECTIVE = 1,

  /**
   * 无效
   *
   * @generated from enum value: CONFIG_STATE_INEFFECTIVE = 2;
   */
  INEFFECTIVE = 2,
}

/**
 * @generated from enum step.raccoon.bonus.CheckInTaskType
 */
export declare enum CheckInTaskType {
  /**
   * @generated from enum value: CHECK_IN_TYPE_UNKNOWN = 0;
   */
  CHECK_IN_TYPE_UNKNOWN = 0,

  /**
   * 常规签到任务
   *
   * @generated from enum value: CHECK_IN_TYPE_NORMAL = 1;
   */
  CHECK_IN_TYPE_NORMAL = 1,

  /**
   * 抽奖签到任务
   *
   * @generated from enum value: CHECK_IN_TYPE_DRAW = 2;
   */
  CHECK_IN_TYPE_DRAW = 2,
}

/**
 * @generated from enum step.raccoon.bonus.DrawCheckInTargetType
 */
export declare enum DrawCheckInTargetType {
  /**
   * @generated from enum value: DRAW_CHECK_IN_TARGET_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: DRAW_CHECK_IN_TARGET_TYPE_FIXED_DATE = 1;
   */
  FIXED_DATE = 1,

  /**
   * @generated from enum value: DRAW_CHECK_IN_TARGET_TYPE_CONSECUTIVE = 2;
   */
  CONSECUTIVE = 2,
}

/**
 * @generated from enum step.raccoon.bonus.TaskType
 */
export declare enum TaskType {
  /**
   * @generated from enum value: TASK_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: TASK_TYPE_NORMAL = 1;
   */
  NORMAL = 1,

  /**
   * @generated from enum value: TASK_TYPE_GROUP = 2;
   */
  GROUP = 2,

  /**
   * @generated from enum value: TASK_TYPE_ACHIEVEMENT = 3;
   */
  ACHIEVEMENT = 3,
}

/**
 * @generated from enum step.raccoon.bonus.NormalTargetType
 */
export declare enum NormalTargetType {
  /**
   * @generated from enum value: NORMAL_TARGET_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: NORMAL_TARGET_TYPE_ONLINE = 1;
   */
  ONLINE = 1,

  /**
   * @generated from enum value: NORMAL_TARGET_TYPE_LIKE_CARD = 2;
   */
  LIKE_CARD = 2,

  /**
   * @generated from enum value: NORMAL_TARGET_TYPE_COMMENT = 3;
   */
  COMMENT = 3,

  /**
   * @generated from enum value: NORMAL_TARGET_TYPE_SHARE = 4;
   */
  SHARE = 4,

  /**
   * @generated from enum value: NORMAL_TARGET_TYPE_PUBLISH = 5;
   */
  PUBLISH = 5,

  /**
   * @generated from enum value: NORMAL_TARGET_TYPE_USER_FOLLOW = 6;
   */
  USER_FOLLOW = 6,
}

/**
 * @generated from enum step.raccoon.bonus.RecordType
 */
export declare enum RecordType {
  /**
   * @generated from enum value: RECORD_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: RECORD_TYPE_CHECK_IN_REWARD = 1;
   */
  CHECK_IN_REWARD = 1,

  /**
   * @generated from enum value: RECORD_TYPE_TASK_REWARD = 2;
   */
  TASK_REWARD = 2,

  /**
   * @generated from enum value: RECORD_TYPE_DRAW_REWARD = 3;
   */
  DRAW_REWARD = 3,
}

/**
 * @generated from enum step.raccoon.bonus.ItemType
 */
export declare enum ItemType {
  /**
   * @generated from enum value: ITEM_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: ITEM_TYPE_DRAW_TICKET = 1;
   */
  DRAW_TICKET = 1,
}

/**
 * @generated from enum step.raccoon.bonus.BehaviorType
 */
export declare enum BehaviorType {
  /**
   * @generated from enum value: BEHAVIOR_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: BEHAVIOR_TYPE_ONLINE = 1;
   */
  ONLINE = 1,

  /**
   * @generated from enum value: BEHAVIOR_TYPE_LIKE_CARD = 2;
   */
  LIKE_CARD = 2,

  /**
   * @generated from enum value: BEHAVIOR_TYPE_COMMENT = 3;
   */
  COMMENT = 3,

  /**
   * @generated from enum value: BEHAVIOR_TYPE_SHARE = 4;
   */
  SHARE = 4,

  /**
   * @generated from enum value: BEHAVIOR_TYPE_PUBLISH = 5;
   */
  PUBLISH = 5,

  /**
   * @generated from enum value: BEHAVIOR_TYPE_USER_FOLLOW = 6;
   */
  USER_FOLLOW = 6,

  /**
   * @generated from enum value: BEHAVIOR_TYPE_BE_LIKED_CARD = 7;
   */
  BE_LIKED_CARD = 7,
}

/**
 * @generated from message step.raccoon.bonus.CheckInRemindMsg
 */
export declare class CheckInRemindMsg extends Message<CheckInRemindMsg> {
  /**
   * @generated from field: int64 user_id = 1;
   */
  userId: bigint;

  constructor(data?: PartialMessage<CheckInRemindMsg>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.bonus.CheckInRemindMsg";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckInRemindMsg;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckInRemindMsg;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckInRemindMsg;

  static equals(a: CheckInRemindMsg | PlainMessage<CheckInRemindMsg> | undefined, b: CheckInRemindMsg | PlainMessage<CheckInRemindMsg> | undefined): boolean;
}

