// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/showcase.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { UserProfile } from "./profile_pb.js";
import type { Media } from "./media_pb.js";
import type { VideoResolution } from "./video_pb.js";
import type { CardType, GameType } from "./types_pb.js";
import type { BrandState, CensoredState } from "./state_pb.js";
import type { BgmInfo, LivePhotoExtInfo } from "./livephoto_pb.js";
import type { ReimagineExtInfo } from "./reimagine_pb.js";
import type { OtakudanceExtInfo } from "./otakudance_pb.js";
import type { JellycatExtInfo } from "./jellycat_pb.js";
import type { BBSPostExtInfo } from "./bbs_pb.js";
import type { PkExtInfo } from "./pk_pb.js";
import type { DanceTogetherExtInfo } from "./dance_together_pb.js";
import type { CardSocialStat } from "./stat_pb.js";
import type { CommentItem } from "./comment_pb.js";

/**
 * @generated from enum step.raccoon.common.TopicType
 */
export declare enum TopicType {
  /**
   * @generated from enum value: TOPIC_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: TOPIC_TYPE_TOPIC = 1;
   */
  TOPIC = 1,

  /**
   * @generated from enum value: TOPIC_TYPE_IP = 2;
   */
  IP = 2,
}

/**
 * 展示图片信息
 *
 * @generated from message step.raccoon.common.DisplayImageInfo
 */
export declare class DisplayImageInfo extends Message<DisplayImageInfo> {
  /**
   * 展示图片url
   *
   * @generated from field: string url = 1;
   */
  url: string;

  /**
   * 该图片的宽度(不保证必有)
   *
   * @generated from field: uint32 width = 2;
   */
  width: number;

  /**
   * 该图片的高度(不保证必有)
   *
   * @generated from field: uint32 height = 3;
   */
  height: number;

  /**
   * 该图片的格式
   *
   * @generated from field: string format = 4;
   */
  format: string;

  constructor(data?: PartialMessage<DisplayImageInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.DisplayImageInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DisplayImageInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DisplayImageInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DisplayImageInfo;

  static equals(a: DisplayImageInfo | PlainMessage<DisplayImageInfo> | undefined, b: DisplayImageInfo | PlainMessage<DisplayImageInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.ExtraCreativeInfo
 */
export declare class ExtraCreativeInfo extends Message<ExtraCreativeInfo> {
  /**
   * feed流运营位创意所需额外信息
   *
   * @generated from field: step.raccoon.common.FeedPositionInfo feed_pos_info = 1;
   */
  feedPosInfo?: FeedPositionInfo;

  constructor(data?: PartialMessage<ExtraCreativeInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ExtraCreativeInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ExtraCreativeInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ExtraCreativeInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ExtraCreativeInfo;

  static equals(a: ExtraCreativeInfo | PlainMessage<ExtraCreativeInfo> | undefined, b: ExtraCreativeInfo | PlainMessage<ExtraCreativeInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.FeedPositionInfo
 */
export declare class FeedPositionInfo extends Message<FeedPositionInfo> {
  /**
   * 账号信息
   *
   * @generated from field: step.raccoon.common.UserProfile user_profile = 1;
   */
  userProfile?: UserProfile;

  /**
   * 标签文案
   *
   * @generated from field: string tag = 2;
   */
  tag: string;

  constructor(data?: PartialMessage<FeedPositionInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.FeedPositionInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FeedPositionInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FeedPositionInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FeedPositionInfo;

  static equals(a: FeedPositionInfo | PlainMessage<FeedPositionInfo> | undefined, b: FeedPositionInfo | PlainMessage<FeedPositionInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.Creative
 */
export declare class Creative extends Message<Creative> {
  /**
   * 创意模板，用于明确样式布局
   *
   * @generated from field: int32 creative_template_id = 1;
   */
  creativeTemplateId: number;

  /**
   * 跳转链接
   *
   * @generated from field: string landing_page = 2;
   */
  landingPage: string;

  /**
   * 填充元素信息
   *
   * @generated from field: repeated step.raccoon.common.CreativeElement elements = 3;
   */
  elements: CreativeElement[];

  /**
   * 其余额外信息
   *
   * @generated from field: map<string, string> extra = 4;
   */
  extra: { [key: string]: string };

  /**
   * 创意指纹
   *
   * @generated from field: string creative_digest = 5;
   */
  creativeDigest: string;

  /**
   * 是否需要预加载
   *
   * @generated from field: bool need_preload = 6;
   */
  needPreload: boolean;

  /**
   * 是否处于展示期间
   *
   * @generated from field: bool displaying = 7;
   */
  displaying: boolean;

  /**
   * 创意id
   *
   * @generated from field: int32 creative_id = 8;
   */
  creativeId: number;

  /**
   * 创意所需额外信息
   *
   * @generated from field: step.raccoon.common.ExtraCreativeInfo extra_creative_info = 100;
   */
  extraCreativeInfo?: ExtraCreativeInfo;

  constructor(data?: PartialMessage<Creative>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Creative";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Creative;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Creative;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Creative;

  static equals(a: Creative | PlainMessage<Creative> | undefined, b: Creative | PlainMessage<Creative> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CreativeElement
 */
export declare class CreativeElement extends Message<CreativeElement> {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: step.raccoon.common.Media media = 2;
   */
  media?: Media;

  constructor(data?: PartialMessage<CreativeElement>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CreativeElement";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreativeElement;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreativeElement;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreativeElement;

  static equals(a: CreativeElement | PlainMessage<CreativeElement> | undefined, b: CreativeElement | PlainMessage<CreativeElement> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CardVideoInfo
 */
export declare class CardVideoInfo extends Message<CardVideoInfo> {
  /**
   * 封面视频宽度
   *
   * @generated from field: uint32 display_video_width = 1;
   */
  displayVideoWidth: number;

  /**
   * 封面视频高度
   *
   * @generated from field: uint32 display_video_height = 2;
   */
  displayVideoHeight: number;

  /**
   * 封面视频id
   *
   * @generated from field: string display_video_id = 3;
   */
  displayVideoId: string;

  /**
   * 视频地址
   *
   * @generated from field: string display_video_url = 4;
   */
  displayVideoUrl: string;

  /**
   * 分辨率档位
   *
   * @generated from field: step.raccoon.common.VideoResolution resolution = 5;
   */
  resolution: VideoResolution;

  constructor(data?: PartialMessage<CardVideoInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardVideoInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardVideoInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardVideoInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardVideoInfo;

  static equals(a: CardVideoInfo | PlainMessage<CardVideoInfo> | undefined, b: CardVideoInfo | PlainMessage<CardVideoInfo> | undefined): boolean;
}

/**
 * 卡片信息
 *
 * @generated from message step.raccoon.common.CardInfo
 */
export declare class CardInfo extends Message<CardInfo> {
  /**
   * 卡片id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 创作者id
   *
   * @generated from field: string uid = 2;
   */
  uid: string;

  /**
   * ip类型
   *
   * @generated from field: int32 brand = 3;
   */
  brand: number;

  /**
   * 卡片类型
   *
   * @generated from field: step.raccoon.common.CardType type = 4;
   */
  type: CardType;

  /**
   * 玩法类型
   *
   * @generated from field: step.raccoon.common.GameType game_type = 5;
   */
  gameType: GameType;

  /**
   * 卡片原图展示图片地址
   *
   * @generated from field: string display_image_url = 6;
   */
  displayImageUrl: string;

  /**
   * 卡片原图宽度
   *
   * @generated from field: uint32 display_image_width = 7;
   */
  displayImageWidth: number;

  /**
   * 卡片原图高度 
   *
   * @generated from field: uint32 display_image_height = 8;
   */
  displayImageHeight: number;

  /**
   * 卡片标题
   *
   * @generated from field: string title = 9;
   */
  title: string;

  /**
   * 玩法内容id
   *
   * @generated from field: string game_id = 10;
   */
  gameId: string;

  /**
   * 卡片展示视频地址
   *
   * @generated from field: string display_video_url = 11;
   */
  displayVideoUrl: string;

  /**
   * 作品包含的角色id
   *
   * @generated from field: repeated string roles = 12;
   */
  roles: string[];

  /**
   * 作品创建的Unix时间戳，单位s
   *
   * @generated from field: int64 create_at = 13;
   */
  createAt: bigint;

  /**
   * 作品更新的Unix时间戳，单位s
   *
   * @generated from field: int64 update_at = 14;
   */
  updateAt: bigint;

  /**
   * 基于原图生成的不同尺寸和格式的展示图片列表
   *
   * @generated from field: repeated step.raccoon.common.DisplayImageInfo display_image_list = 15 [deprecated = true];
   * @deprecated
   */
  displayImageList: DisplayImageInfo[];

  /**
   * 安全状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored = 16;
   */
  censored: CensoredState;

  /**
   * 扩展字段，各玩法自定
   *
   * @generated from field: string ext_info = 17;
   */
  extInfo: string;

  /**
   * 运营资源位数据
   *
   * @generated from field: step.raccoon.common.ResourceInfo resource_info = 18;
   */
  resourceInfo?: ResourceInfo;

  /**
   * 卡片原图id
   *
   * @generated from field: string display_image_id = 19;
   */
  displayImageId: string;

  /**
   * 封面视频宽度
   *
   * @generated from field: uint32 display_video_width = 20;
   */
  displayVideoWidth: number;

  /**
   * 封面视频高度
   *
   * @generated from field: uint32 display_video_height = 21;
   */
  displayVideoHeight: number;

  /**
   * 封面视频id
   *
   * @generated from field: string display_video_id = 22;
   */
  displayVideoId: string;

  /**
   * 视频信息
   *
   * @generated from field: repeated step.raccoon.common.CardVideoInfo card_videos = 23;
   */
  cardVideos: CardVideoInfo[];

  /**
   * 额外信息，for不同类型玩法
   *
   * @generated from field: step.raccoon.common.CardExtInfo card_ext_info = 99;
   */
  cardExtInfo?: CardExtInfo;

  /**
   * 非捏图卡片的extInfo
   *
   * @generated from oneof step.raccoon.common.CardInfo.imitation_card_info
   */
  imitationCardInfo: {
    /**
     * @generated from field: step.raccoon.common.BrandCardInfo brand_card_info = 100;
     */
    value: BrandCardInfo;
    case: "brandCardInfo";
  } | {
    /**
     * @generated from field: step.raccoon.common.WorldTopicCardInfo world_topic_card_info = 101;
     */
    value: WorldTopicCardInfo;
    case: "worldTopicCardInfo";
  } | {
    /**
     * @generated from field: step.raccoon.common.CreativeCardInfo creative_card_info = 102;
     */
    value: CreativeCardInfo;
    case: "creativeCardInfo";
  } | {
    /**
     * @generated from field: step.raccoon.common.GameTemplateExtInfo game_template_info = 103;
     */
    value: GameTemplateExtInfo;
    case: "gameTemplateInfo";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<CardInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardInfo;

  static equals(a: CardInfo | PlainMessage<CardInfo> | undefined, b: CardInfo | PlainMessage<CardInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CardExtInfo
 */
export declare class CardExtInfo extends Message<CardExtInfo> {
  /**
   * @generated from oneof step.raccoon.common.CardExtInfo.value
   */
  value: {
    /**
     * @generated from field: step.raccoon.common.LivePhotoExtInfo livephoto = 1;
     */
    value: LivePhotoExtInfo;
    case: "livephoto";
  } | {
    /**
     * @generated from field: step.raccoon.common.ReimagineExtInfo reimagine = 2;
     */
    value: ReimagineExtInfo;
    case: "reimagine";
  } | {
    /**
     * @generated from field: step.raccoon.common.OtakudanceExtInfo otakudance = 3;
     */
    value: OtakudanceExtInfo;
    case: "otakudance";
  } | {
    /**
     * 挂件
     *
     * @generated from field: step.raccoon.common.JellycatExtInfo jellycat = 4;
     */
    value: JellycatExtInfo;
    case: "jellycat";
  } | {
    /**
     * @generated from field: step.raccoon.common.BBSPostExtInfo bbs = 5;
     */
    value: BBSPostExtInfo;
    case: "bbs";
  } | {
    /**
     * @generated from field: step.raccoon.common.PkExtInfo pk = 6;
     */
    value: PkExtInfo;
    case: "pk";
  } | {
    /**
     * @generated from field: step.raccoon.common.DanceTogetherExtInfo dance_together = 7;
     */
    value: DanceTogetherExtInfo;
    case: "danceTogether";
  } | {
    /**
     * 通用透传
     *
     * @generated from field: bytes business_info = 8;
     */
    value: Uint8Array;
    case: "businessInfo";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<CardExtInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardExtInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardExtInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardExtInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardExtInfo;

  static equals(a: CardExtInfo | PlainMessage<CardExtInfo> | undefined, b: CardExtInfo | PlainMessage<CardExtInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.GameTemplateExtInfo
 */
export declare class GameTemplateExtInfo extends Message<GameTemplateExtInfo> {
  /**
   * 模板唯一id
   *
   * @generated from field: string template_id = 1;
   */
  templateId: string;

  /**
   * 在玩人数
   *
   * @generated from field: int32 play_user_cnt = 2;
   */
  playUserCnt: number;

  /**
   * @generated from field: string bg_image_id = 3;
   */
  bgImageId: string;

  /**
   * @generated from field: string bg_image_url = 4;
   */
  bgImageUrl: string;

  /**
   * @generated from field: uint32 bg_image_width = 5;
   */
  bgImageWidth: number;

  /**
   * @generated from field: uint32 bg_image_height = 6;
   */
  bgImageHeight: number;

  /**
   * @generated from field: string template_name = 7;
   */
  templateName: string;

  constructor(data?: PartialMessage<GameTemplateExtInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.GameTemplateExtInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GameTemplateExtInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GameTemplateExtInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GameTemplateExtInfo;

  static equals(a: GameTemplateExtInfo | PlainMessage<GameTemplateExtInfo> | undefined, b: GameTemplateExtInfo | PlainMessage<GameTemplateExtInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.ResourceInfo
 */
export declare class ResourceInfo extends Message<ResourceInfo> {
  /**
   * 资源位
   *
   * @generated from field: repeated step.raccoon.common.Resource resource_list = 1;
   */
  resourceList: Resource[];

  constructor(data?: PartialMessage<ResourceInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ResourceInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResourceInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResourceInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResourceInfo;

  static equals(a: ResourceInfo | PlainMessage<ResourceInfo> | undefined, b: ResourceInfo | PlainMessage<ResourceInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.Resource
 */
export declare class Resource extends Message<Resource> {
  /**
   * 图片信息
   *
   * @generated from field: step.raccoon.common.DisplayImageInfo image = 1;
   */
  image?: DisplayImageInfo;

  /**
   * 跳转地址
   *
   * @generated from field: string jump_schema = 2;
   */
  jumpSchema: string;

  constructor(data?: PartialMessage<Resource>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Resource";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Resource;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Resource;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Resource;

  static equals(a: Resource | PlainMessage<Resource> | undefined, b: Resource | PlainMessage<Resource> | undefined): boolean;
}

/**
 * 作品品牌信息
 *
 * @generated from message step.raccoon.common.BrandInfo
 */
export declare class BrandInfo extends Message<BrandInfo> {
  /**
   * ip类型
   *
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * 显示标题
   *
   * @generated from field: string display_name = 2;
   */
  displayName: string;

  /**
   * 图标地址
   *
   * @generated from field: string icon_url = 3;
   */
  iconUrl: string;

  /**
   * 背景颜色
   *
   * @generated from field: string bg_color = 4;
   */
  bgColor: string;

  /**
   * 是否热门
   *
   * @generated from field: bool hot = 5;
   */
  hot: boolean;

  /**
   * 详情页背景图片链接
   *
   * @generated from field: string detail_bg_img_url = 6;
   */
  detailBgImgUrl: string;

  /**
   * ip描述
   *
   * @generated from field: string description = 7;
   */
  description: string;

  /**
   * ip上线状态
   *
   * @generated from field: step.raccoon.common.BrandState state = 8;
   */
  state: BrandState;

  /**
   * 是否为新上brand
   *
   * @generated from field: bool is_new = 9;
   */
  isNew: boolean;

  /**
   * brand发布日期
   *
   * @generated from field: step.raccoon.common.PublishDate publish_date = 10;
   */
  publishDate?: PublishDate;

  /**
   * 是否已预约
   *
   * @generated from field: bool reserved = 11;
   */
  reserved: boolean;

  /**
   * 详情页icon图片
   *
   * @generated from field: string landing_icon = 12;
   */
  landingIcon: string;

  constructor(data?: PartialMessage<BrandInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.BrandInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BrandInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BrandInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BrandInfo;

  static equals(a: BrandInfo | PlainMessage<BrandInfo> | undefined, b: BrandInfo | PlainMessage<BrandInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.PublishDate
 */
export declare class PublishDate extends Message<PublishDate> {
  /**
   * @generated from field: int32 month = 2;
   */
  month: number;

  /**
   * @generated from field: int32 day = 3;
   */
  day: number;

  constructor(data?: PartialMessage<PublishDate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.PublishDate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PublishDate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PublishDate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PublishDate;

  static equals(a: PublishDate | PlainMessage<PublishDate> | undefined, b: PublishDate | PlainMessage<PublishDate> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.BBSInfo
 */
export declare class BBSInfo extends Message<BBSInfo> {
  /**
   * bbs封面
   *
   * @generated from field: string bbs_cover_image_id = 1;
   */
  bbsCoverImageId: string;

  /**
   * bbs封面
   *
   * @generated from field: string bbs_cover_image_url = 2;
   */
  bbsCoverImageUrl: string;

  /**
   * 宽度
   *
   * @generated from field: uint32 bbs_cover_image_width = 3;
   */
  bbsCoverImageWidth: number;

  /**
   * 高度 
   *
   * @generated from field: uint32 bbs_cover_image_height = 4;
   */
  bbsCoverImageHeight: number;

  constructor(data?: PartialMessage<BBSInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.BBSInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BBSInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BBSInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BBSInfo;

  static equals(a: BBSInfo | PlainMessage<BBSInfo> | undefined, b: BBSInfo | PlainMessage<BBSInfo> | undefined): boolean;
}

/**
 * 带社交统计信息的卡片信息
 *
 * @generated from message step.raccoon.common.RichCardInfo
 */
export declare class RichCardInfo extends Message<RichCardInfo> {
  /**
   * 卡片基础信息
   *
   * @generated from field: step.raccoon.common.CardInfo card = 1;
   */
  card?: CardInfo;

  /**
   * 创作者用户信息
   *
   * @generated from field: step.raccoon.common.UserProfile user = 2;
   */
  user?: UserProfile;

  /**
   * 卡片社交统计数据
   *
   * @generated from field: step.raccoon.common.CardSocialStat social_stat = 3;
   */
  socialStat?: CardSocialStat;

  /**
   * 透传推荐回传埋点
   *
   * @generated from field: string recExtraData = 4;
   */
  recExtraData: string;

  /**
   * 话题信息
   *
   * @generated from field: step.raccoon.common.TopicInfo topic = 5;
   */
  topic?: TopicInfo;

  /**
   * 评论信息
   *
   * @generated from field: step.raccoon.common.CommentItem comment = 6;
   */
  comment?: CommentItem;

  /**
   * 完整话题信息
   *
   * @generated from field: repeated step.raccoon.common.TopicInfo topics = 7;
   */
  topics: TopicInfo[];

  /**
   * 推荐debug信息
   *
   * @generated from field: string rec_debug_info = 8;
   */
  recDebugInfo: string;

  /**
   * 额外bbs信息
   *
   * @generated from field: step.raccoon.common.BBSInfo bbs_info = 9;
   */
  bbsInfo?: BBSInfo;

  /**
   * 评论的用户，最多返回10个 废弃，勿用
   *
   * @generated from field: repeated step.raccoon.common.UserProfile comment_users = 10;
   */
  commentUsers: UserProfile[];

  /**
   * 关联的音乐
   *
   * @generated from field: step.raccoon.common.BgmInfo relate_bgm = 11;
   */
  relateBgm?: BgmInfo;

  /**
   * 关注信息
   *
   * @generated from field: step.raccoon.common.FollowInfo follow_info = 12;
   */
  followInfo?: FollowInfo;

  constructor(data?: PartialMessage<RichCardInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.RichCardInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RichCardInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RichCardInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RichCardInfo;

  static equals(a: RichCardInfo | PlainMessage<RichCardInfo> | undefined, b: RichCardInfo | PlainMessage<RichCardInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.FollowInfo
 */
export declare class FollowInfo extends Message<FollowInfo> {
  /**
   * @generated from field: bool is_followed = 1;
   */
  isFollowed: boolean;

  /**
   * @generated from field: bool is_fan = 2;
   */
  isFan: boolean;

  constructor(data?: PartialMessage<FollowInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.FollowInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FollowInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FollowInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FollowInfo;

  static equals(a: FollowInfo | PlainMessage<FollowInfo> | undefined, b: FollowInfo | PlainMessage<FollowInfo> | undefined): boolean;
}

/**
 * 卡片的创作者信息和社交统计数据
 *
 * @generated from message step.raccoon.common.CardSocialInfo
 */
export declare class CardSocialInfo extends Message<CardSocialInfo> {
  /**
   * @generated from field: step.raccoon.common.UserProfile profile = 1;
   */
  profile?: UserProfile;

  /**
   * @generated from field: step.raccoon.common.CardSocialStat stat = 2;
   */
  stat?: CardSocialStat;

  constructor(data?: PartialMessage<CardSocialInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardSocialInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardSocialInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardSocialInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardSocialInfo;

  static equals(a: CardSocialInfo | PlainMessage<CardSocialInfo> | undefined, b: CardSocialInfo | PlainMessage<CardSocialInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CardMetadata
 */
export declare class CardMetadata extends Message<CardMetadata> {
  /**
   * 作品可扩展用的属性描述, sharable: true/false
   *
   * @generated from field: map<string, string> attrs = 1;
   */
  attrs: { [key: string]: string };

  constructor(data?: PartialMessage<CardMetadata>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardMetadata";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardMetadata;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardMetadata;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardMetadata;

  static equals(a: CardMetadata | PlainMessage<CardMetadata> | undefined, b: CardMetadata | PlainMessage<CardMetadata> | undefined): boolean;
}

/**
 * 活动配置
 *
 * @generated from message step.raccoon.common.ActivityConf
 */
export declare class ActivityConf extends Message<ActivityConf> {
  /**
   * @generated from field: string tag_text = 1;
   */
  tagText: string;

  /**
   * @generated from field: string redirect_url = 2;
   */
  redirectUrl: string;

  /**
   * @generated from field: string button_text = 3;
   */
  buttonText: string;

  constructor(data?: PartialMessage<ActivityConf>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ActivityConf";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ActivityConf;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ActivityConf;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ActivityConf;

  static equals(a: ActivityConf | PlainMessage<ActivityConf> | undefined, b: ActivityConf | PlainMessage<ActivityConf> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.TopicInfo
 */
export declare class TopicInfo extends Message<TopicInfo> {
  /**
   * 话题id
   *
   * @generated from field: string topic_id = 1;
   */
  topicId: string;

  /**
   * 话题名称
   *
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * 背景颜色
   *
   * @generated from field: string background_color = 3;
   */
  backgroundColor: string;

  /**
   * 话题配置
   *
   * @generated from field: step.raccoon.common.ActivityConf activity_conf = 4;
   */
  activityConf?: ActivityConf;

  /**
   * 话题类型
   *
   * @generated from field: step.raccoon.common.TopicType topic_type = 5;
   */
  topicType: TopicType;

  constructor(data?: PartialMessage<TopicInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.TopicInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TopicInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TopicInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TopicInfo;

  static equals(a: TopicInfo | PlainMessage<TopicInfo> | undefined, b: TopicInfo | PlainMessage<TopicInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.BrandCardInfo
 */
export declare class BrandCardInfo extends Message<BrandCardInfo> {
  /**
   * ip 类型
   *
   * @generated from field: int32 brand = 1;
   */
  brand: number;

  /**
   * 显示标题
   *
   * @generated from field: string display_name = 2;
   */
  displayName: string;

  /**
   * 图标地址
   *
   * @generated from field: string icon_url = 3;
   */
  iconUrl: string;

  constructor(data?: PartialMessage<BrandCardInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.BrandCardInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BrandCardInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BrandCardInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BrandCardInfo;

  static equals(a: BrandCardInfo | PlainMessage<BrandCardInfo> | undefined, b: BrandCardInfo | PlainMessage<BrandCardInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.WorldTopicCardInfo
 */
export declare class WorldTopicCardInfo extends Message<WorldTopicCardInfo> {
  /**
   * 内容id
   *
   * @generated from field: string card_id = 1;
   */
  cardId: string;

  /**
   * 标题
   *
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * 平行世界数量
   *
   * @generated from field: int32 total_worlds = 3;
   */
  totalWorlds: number;

  /**
   * 3个用户头像
   *
   * @generated from field: repeated string top_user_avatars = 4;
   */
  topUserAvatars: string[];

  /**
   * 话题封面
   *
   * @generated from field: step.raccoon.common.WorldCover cover = 5;
   */
  cover?: WorldCover;

  constructor(data?: PartialMessage<WorldTopicCardInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.WorldTopicCardInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorldTopicCardInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorldTopicCardInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorldTopicCardInfo;

  static equals(a: WorldTopicCardInfo | PlainMessage<WorldTopicCardInfo> | undefined, b: WorldTopicCardInfo | PlainMessage<WorldTopicCardInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.WorldCover
 */
export declare class WorldCover extends Message<WorldCover> {
  /**
   * image key
   *
   * @generated from field: string ImageId = 1;
   */
  ImageId: string;

  /**
   * image url
   *
   * @generated from field: string ImageUrl = 2;
   */
  ImageUrl: string;

  /**
   * 平均颜色值，只在topic cover生效。原样存储
   *
   * @generated from field: string Color = 3;
   */
  Color: string;

  constructor(data?: PartialMessage<WorldCover>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.WorldCover";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorldCover;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorldCover;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorldCover;

  static equals(a: WorldCover | PlainMessage<WorldCover> | undefined, b: WorldCover | PlainMessage<WorldCover> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CreativeCardInfo
 */
export declare class CreativeCardInfo extends Message<CreativeCardInfo> {
  /**
   * 运营位可展示创意内容
   *
   * @generated from field: repeated step.raccoon.common.Creative creatives = 1;
   */
  creatives: Creative[];

  constructor(data?: PartialMessage<CreativeCardInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CreativeCardInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreativeCardInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreativeCardInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreativeCardInfo;

  static equals(a: CreativeCardInfo | PlainMessage<CreativeCardInfo> | undefined, b: CreativeCardInfo | PlainMessage<CreativeCardInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.CardViewerCtrl
 */
export declare class CardViewerCtrl extends Message<CardViewerCtrl> {
  /**
   * 可分享
   *
   * @generated from field: bool sharable = 1;
   */
  sharable: boolean;

  /**
   * 可下载
   *
   * @generated from field: bool downloadable = 2;
   */
  downloadable: boolean;

  /**
   * 是否正在审核中
   *
   * @generated from field: bool censoring = 3;
   */
  censoring: boolean;

  /**
   * 客户端提示控制toast词
   *
   * @generated from field: map<string, string> toasts = 4;
   */
  toasts: { [key: string]: string };

  constructor(data?: PartialMessage<CardViewerCtrl>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.CardViewerCtrl";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CardViewerCtrl;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CardViewerCtrl;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CardViewerCtrl;

  static equals(a: CardViewerCtrl | PlainMessage<CardViewerCtrl> | undefined, b: CardViewerCtrl | PlainMessage<CardViewerCtrl> | undefined): boolean;
}

