// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/common/video.proto (package step.raccoon.common, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { GameType } from "./types_pb.js";
import type { RoleBID } from "./role_pb.js";

/**
 * @generated from enum step.raccoon.common.VideoResolution
 */
export declare enum VideoResolution {
  /**
   * 原画质
   *
   * @generated from enum value: VIDEO_RESOLUTION_ORIGINAL = 0;
   */
  VIDEO_RESOLUTION_ORIGINAL = 0,

  /**
   * 480p
   *
   * @generated from enum value: VIDEO_RESOLUTION_480P = 1;
   */
  VIDEO_RESOLUTION_480P = 1,

  /**
   * 720p
   *
   * @generated from enum value: VIDEO_RESOLUTION_720P = 2;
   */
  VIDEO_RESOLUTION_720P = 2,

  /**
   * 1080p
   *
   * @generated from enum value: VIDEO_RESOLUTION_1080P = 3;
   */
  VIDEO_RESOLUTION_1080P = 3,
}

/**
 * @generated from enum step.raccoon.common.VideoType
 */
export declare enum VideoType {
  /**
   * @generated from enum value: VIDEO_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: VIDEO_TYPE_LIVEPHOTO = 1;
   */
  LIVEPHOTO = 1,

  /**
   * @generated from enum value: VIDEO_TYPE_REIMAGINE = 2;
   */
  REIMAGINE = 2,

  /**
   * 宅舞视频
   *
   * @generated from enum value: VIDEO_TYPE_OTAKUDANCE = 3;
   */
  OTAKUDANCE = 3,

  /**
   * 春节一起跳
   *
   * @generated from enum value: VIDEO_TYPE_DANCE_TOGETHER = 4;
   */
  DANCE_TOGETHER = 4,

  /**
   * 图生视频
   *
   * @generated from enum value: VIDEO_TYPE_I2V = 5;
   */
  I2V = 5,
}

/**
 * @generated from enum step.raccoon.common.VideoStatus
 */
export declare enum VideoStatus {
  /**
   * @generated from enum value: VIDEO_STATUS_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * 排队
   *
   * @generated from enum value: VIDEO_STATUS_WAITING = 1;
   */
  WAITING = 1,

  /**
   * 处理中
   *
   * @generated from enum value: VIDEO_STATUS_PROCESSING = 2;
   */
  PROCESSING = 2,

  /**
   * 安全审核失败
   *
   * @generated from enum value: VIDEO_STATUS_RISK_FAIL = 996;
   */
  RISK_FAIL = 996,

  /**
   * 失败
   *
   * @generated from enum value: VIDEO_STATUS_FAIL = 997;
   */
  FAIL = 997,

  /**
   * 成功
   *
   * @generated from enum value: VIDEO_STATUS_SUC = 998;
   */
  SUC = 998,

  /**
   * 取消
   *
   * @generated from enum value: VIDEO_STATUS_CANCEL = 999;
   */
  CANCEL = 999,
}

/**
 * @generated from enum step.raccoon.common.MediaAspectRatio
 */
export declare enum MediaAspectRatio {
  /**
   * @generated from enum value: MEDIA_ASPECT_UNKNOWN = 0;
   */
  MEDIA_ASPECT_UNKNOWN = 0,

  /**
   * @generated from enum value: MEDIA_ASPECT_RATIO_1x1 = 1;
   */
  MEDIA_ASPECT_RATIO_1x1 = 1,

  /**
   * @generated from enum value: MEDIA_ASPECT_RATIO_4x3 = 2;
   */
  MEDIA_ASPECT_RATIO_4x3 = 2,

  /**
   * @generated from enum value: MEDIA_ASPECT_RATION_3x4 = 3;
   */
  MEDIA_ASPECT_RATION_3x4 = 3,

  /**
   * @generated from enum value: MEDIA_ASPECT_RATIO_16x9 = 4;
   */
  MEDIA_ASPECT_RATIO_16x9 = 4,

  /**
   * @generated from enum value: MEDIA_ASPECT_RATION_9x16 = 5;
   */
  MEDIA_ASPECT_RATION_9x16 = 5,
}

/**
 * @generated from enum step.raccoon.common.KeLingDuration
 */
export declare enum KeLingDuration {
  /**
   * @generated from enum value: KELING_DURATION_UNKNOWN = 0;
   */
  KELING_DURATION_UNKNOWN = 0,

  /**
   * @generated from enum value: KELING_DURATION_5_SECONDS = 1;
   */
  KELING_DURATION_5_SECONDS = 1,

  /**
   * @generated from enum value: KELING_DURATION_10_SECONDS = 2;
   */
  KELING_DURATION_10_SECONDS = 2,
}

/**
 * @generated from enum step.raccoon.common.KeLingVideoMode
 */
export declare enum KeLingVideoMode {
  /**
   * @generated from enum value: KELING_VIDEO_MODE_UNKNOWN = 0;
   */
  KELING_VIDEO_MODE_UNKNOWN = 0,

  /**
   * @generated from enum value: KELING_VIDEO_MODE_STD = 1;
   */
  KELING_VIDEO_MODE_STD = 1,

  /**
   * @generated from enum value: KELING_VIDEO_MODE_PRO = 2;
   */
  KELING_VIDEO_MODE_PRO = 2,
}

/**
 * @generated from enum step.raccoon.common.KeLingVideoModel
 */
export declare enum KeLingVideoModel {
  /**
   * @generated from enum value: KELING_VIDEO_MODEL_UNKNOWN = 0;
   */
  KELING_VIDEO_MODEL_UNKNOWN = 0,

  /**
   * @generated from enum value: KELING_VIDEO_MODEL_V1_5 = 1;
   */
  KELING_VIDEO_MODEL_V1_5 = 1,

  /**
   * @generated from enum value: KELING_VIDEO_MODEL_V1_6 = 2;
   */
  KELING_VIDEO_MODEL_V1_6 = 2,
}

/**
 * @generated from message step.raccoon.common.VideoSize
 */
export declare class VideoSize extends Message<VideoSize> {
  /**
   * @generated from field: int32 width = 1;
   */
  width: number;

  /**
   * @generated from field: int32 height = 2;
   */
  height: number;

  /**
   * @generated from field: step.raccoon.common.MediaAspectRatio aspect_ration = 3;
   */
  aspectRation: MediaAspectRatio;

  constructor(data?: PartialMessage<VideoSize>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.VideoSize";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VideoSize;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VideoSize;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VideoSize;

  static equals(a: VideoSize | PlainMessage<VideoSize> | undefined, b: VideoSize | PlainMessage<VideoSize> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.VideoInfo
 */
export declare class VideoInfo extends Message<VideoInfo> {
  /**
   * @generated from field: int64 video_id = 1;
   */
  videoId: bigint;

  /**
   * @generated from field: string biz_id = 2;
   */
  bizId: string;

  /**
   * @generated from field: int64 uid = 3;
   */
  uid: bigint;

  /**
   * @generated from field: step.raccoon.common.VideoStatus status = 4;
   */
  status: VideoStatus;

  /**
   * @generated from field: step.raccoon.common.VideoSize size = 5;
   */
  size?: VideoSize;

  /**
   * @generated from field: string video_url = 6;
   */
  videoUrl: string;

  /**
   * 视频首帧
   *
   * @generated from field: string video_first_frame_url = 7;
   */
  videoFirstFrameUrl: string;

  /**
   * 源媒体id
   *
   * @generated from field: string media_video_id = 8;
   */
  mediaVideoId: string;

  /**
   * 首帧源图id
   *
   * @generated from field: string video_first_frame_img_id = 9;
   */
  videoFirstFrameImgId: string;

  /**
   * @generated from field: step.raccoon.common.VideoExtraParam extra_param = 10;
   */
  extraParam?: VideoExtraParam;

  /**
   * @generated from field: string trace_id = 11;
   */
  traceId: string;

  /**
   * @generated from field: step.raccoon.common.GameType game_type = 12;
   */
  gameType: GameType;

  /**
   * @generated from field: step.raccoon.common.VideoType video_type = 13;
   */
  videoType: VideoType;

  /**
   * 视频bgm
   *
   * @generated from field: string video_bgm_url = 14;
   */
  videoBgmUrl: string;

  /**
   * bgm源音频id
   *
   * @generated from field: string video_bgm_audio_id = 15;
   */
  videoBgmAudioId: string;

  constructor(data?: PartialMessage<VideoInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.VideoInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VideoInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VideoInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VideoInfo;

  static equals(a: VideoInfo | PlainMessage<VideoInfo> | undefined, b: VideoInfo | PlainMessage<VideoInfo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.VideoExtraParam
 */
export declare class VideoExtraParam extends Message<VideoExtraParam> {
  /**
   * 希望使用的模型名
   *
   * @generated from field: string model_name = 1;
   */
  modelName: string;

  /**
   * 执行使用的模型名
   *
   * @generated from field: string exec_model_name = 2;
   */
  execModelName: string;

  /**
   * @generated from oneof step.raccoon.common.VideoExtraParam.param
   */
  param: {
    /**
     * @generated from field: step.raccoon.common.LivePhotoParam live_photo = 101;
     */
    value: LivePhotoParam;
    case: "livePhoto";
  } | {
    /**
     * @generated from field: step.raccoon.common.ReimagineParam reimagine = 102;
     */
    value: ReimagineParam;
    case: "reimagine";
  } | {
    /**
     * 宅舞
     *
     * @generated from field: step.raccoon.common.OtakudanceParam otakudance = 103;
     */
    value: OtakudanceParam;
    case: "otakudance";
  } | {
    /**
     * 爆爆爆
     *
     * @generated from field: step.raccoon.common.DanceTogetherParam dance_together = 104;
     */
    value: DanceTogetherParam;
    case: "danceTogether";
  } | {
    /**
     * 图生视频
     *
     * @generated from field: step.raccoon.common.Image2VideoParam i2v = 105;
     */
    value: Image2VideoParam;
    case: "i2v";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<VideoExtraParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.VideoExtraParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VideoExtraParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VideoExtraParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VideoExtraParam;

  static equals(a: VideoExtraParam | PlainMessage<VideoExtraParam> | undefined, b: VideoExtraParam | PlainMessage<VideoExtraParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.Image2VideoParam
 */
export declare class Image2VideoParam extends Message<Image2VideoParam> {
  /**
   * @generated from field: string image_id = 1;
   */
  imageId: string;

  /**
   * 正向文本词
   *
   * @generated from field: string prompt = 2;
   */
  prompt: string;

  /**
   * 负向文本词
   *
   * @generated from field: string negative_prompt = 3;
   */
  negativePrompt: string;

  /**
   * 图片裁切参数
   *
   * @generated from field: string crop_param = 4;
   */
  cropParam: string;

  /**
   * @generated from oneof step.raccoon.common.Image2VideoParam.extra_param
   */
  extraParam: {
    /**
     * @generated from field: step.raccoon.common.KeLingI2VParam keling_param = 101;
     */
    value: KeLingI2VParam;
    case: "kelingParam";
  } | {
    /**
     * @generated from field: step.raccoon.common.StepI2vParam step_param = 102;
     */
    value: StepI2vParam;
    case: "stepParam";
  } | {
    /**
     * @generated from field: step.raccoon.common.WanParam wan_param = 103;
     */
    value: WanParam;
    case: "wanParam";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<Image2VideoParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.Image2VideoParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Image2VideoParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Image2VideoParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Image2VideoParam;

  static equals(a: Image2VideoParam | PlainMessage<Image2VideoParam> | undefined, b: Image2VideoParam | PlainMessage<Image2VideoParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.KeLingI2VParam
 */
export declare class KeLingI2VParam extends Message<KeLingI2VParam> {
  /**
   * 生成时长
   *
   * @generated from field: step.raccoon.common.KeLingDuration duration = 1;
   */
  duration: KeLingDuration;

  /**
   * 自由度参数 [0,1]
   *
   * @generated from field: double cfg_scale = 2;
   */
  cfgScale: number;

  /**
   * 模式
   *
   * @generated from field: step.raccoon.common.KeLingVideoMode mode = 3;
   */
  mode: KeLingVideoMode;

  /**
   * 可灵内部模型区分
   *
   * @generated from field: step.raccoon.common.KeLingVideoModel model_name = 4;
   */
  modelName: KeLingVideoModel;

  constructor(data?: PartialMessage<KeLingI2VParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.KeLingI2VParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KeLingI2VParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KeLingI2VParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KeLingI2VParam;

  static equals(a: KeLingI2VParam | PlainMessage<KeLingI2VParam> | undefined, b: KeLingI2VParam | PlainMessage<KeLingI2VParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.StepI2vParam
 */
export declare class StepI2vParam extends Message<StepI2vParam> {
  /**
   * @generated from field: double motion_score = 1;
   */
  motionScore: number;

  constructor(data?: PartialMessage<StepI2vParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.StepI2vParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StepI2vParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StepI2vParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StepI2vParam;

  static equals(a: StepI2vParam | PlainMessage<StepI2vParam> | undefined, b: StepI2vParam | PlainMessage<StepI2vParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.WanParam
 */
export declare class WanParam extends Message<WanParam> {
  /**
   * @generated from field: double motion_score = 1;
   */
  motionScore: number;

  /**
   * @generated from field: string model_name = 2;
   */
  modelName: string;

  constructor(data?: PartialMessage<WanParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.WanParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WanParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WanParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WanParam;

  static equals(a: WanParam | PlainMessage<WanParam> | undefined, b: WanParam | PlainMessage<WanParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.LivePhotoParam
 */
export declare class LivePhotoParam extends Message<LivePhotoParam> {
  /**
   * @generated from field: string input_prompt = 1;
   */
  inputPrompt: string;

  /**
   * @generated from field: string input_image_id = 2;
   */
  inputImageId: string;

  /**
   * @generated from field: double motion_score = 3;
   */
  motionScore: number;

  /**
   * @generated from field: string crop_param = 4;
   */
  cropParam: string;

  constructor(data?: PartialMessage<LivePhotoParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.LivePhotoParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LivePhotoParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LivePhotoParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LivePhotoParam;

  static equals(a: LivePhotoParam | PlainMessage<LivePhotoParam> | undefined, b: LivePhotoParam | PlainMessage<LivePhotoParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.ReimagineParam
 */
export declare class ReimagineParam extends Message<ReimagineParam> {
  /**
   * @generated from field: string prompt = 1;
   */
  prompt: string;

  /**
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  /**
   * @generated from field: bool is_join = 3;
   */
  isJoin: boolean;

  constructor(data?: PartialMessage<ReimagineParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.ReimagineParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReimagineParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReimagineParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReimagineParam;

  static equals(a: ReimagineParam | PlainMessage<ReimagineParam> | undefined, b: ReimagineParam | PlainMessage<ReimagineParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.OtakudanceParam
 */
export declare class OtakudanceParam extends Message<OtakudanceParam> {
  /**
   * 角色id/brand id，用于proto记录
   *
   * @generated from field: repeated step.raccoon.common.RoleBID role_bids = 1;
   */
  roleBids: RoleBID[];

  /**
   * 玩法用户侧prompt
   *
   * @generated from field: string prompt = 2;
   */
  prompt: string;

  /**
   * 生视频图片 url
   *
   * @generated from field: string image_url = 3;
   */
  imageUrl: string;

  /**
   * 模型参数
   *
   * @generated from field: string model_params = 4;
   */
  modelParams: string;

  /**
   * 驱动素材id
   *
   * @generated from field: string hyb_id = 5;
   */
  hybId: string;

  /**
   * 驱动视频url
   *
   * @generated from field: string hyb_video_url = 6;
   */
  hybVideoUrl: string;

  /**
   * 驱动pose数据文件url
   *
   * @generated from field: string hyb_pose_url = 7;
   */
  hybPoseUrl: string;

  constructor(data?: PartialMessage<OtakudanceParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.OtakudanceParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OtakudanceParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OtakudanceParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OtakudanceParam;

  static equals(a: OtakudanceParam | PlainMessage<OtakudanceParam> | undefined, b: OtakudanceParam | PlainMessage<OtakudanceParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.DanceTogetherParam
 */
export declare class DanceTogetherParam extends Message<DanceTogetherParam> {
  /**
   * @generated from field: string prompt = 1;
   */
  prompt: string;

  /**
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  /**
   * @generated from field: int32 duration = 3;
   */
  duration: number;

  constructor(data?: PartialMessage<DanceTogetherParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.DanceTogetherParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DanceTogetherParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DanceTogetherParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DanceTogetherParam;

  static equals(a: DanceTogetherParam | PlainMessage<DanceTogetherParam> | undefined, b: DanceTogetherParam | PlainMessage<DanceTogetherParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.common.VideoStatusUpdateNotifyEvent
 */
export declare class VideoStatusUpdateNotifyEvent extends Message<VideoStatusUpdateNotifyEvent> {
  /**
   * @generated from field: int64 video_id = 1;
   */
  videoId: bigint;

  /**
   * @generated from field: string biz_id = 2;
   */
  bizId: string;

  /**
   * @generated from field: step.raccoon.common.VideoStatus new_status = 3;
   */
  newStatus: VideoStatus;

  /**
   * @generated from field: step.raccoon.common.GameType game_type = 4;
   */
  gameType: GameType;

  constructor(data?: PartialMessage<VideoStatusUpdateNotifyEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.common.VideoStatusUpdateNotifyEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VideoStatusUpdateNotifyEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VideoStatusUpdateNotifyEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VideoStatusUpdateNotifyEvent;

  static equals(a: VideoStatusUpdateNotifyEvent | PlainMessage<VideoStatusUpdateNotifyEvent> | undefined, b: VideoStatusUpdateNotifyEvent | PlainMessage<VideoStatusUpdateNotifyEvent> | undefined): boolean;
}

