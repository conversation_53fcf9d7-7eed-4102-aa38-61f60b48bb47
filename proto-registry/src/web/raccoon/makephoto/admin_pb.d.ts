// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/makephoto/admin.proto (package step.raccoon.makephoto, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { CensoredState } from "../common/state_pb.js";
import type { PagePagination, Pagination, QueryOrderItem, Timerange } from "../common/utils_pb.js";
import type { StyleType } from "../common/assets_pb.js";

/**
 * @generated from enum step.raccoon.makephoto.GameStyleMode
 */
export declare enum GameStyleMode {
  /**
   * 未知
   *
   * @generated from enum value: MODE_UNKOWN = 0;
   */
  MODE_UNKOWN = 0,

  /**
   * 捏图单人
   *
   * @generated from enum value: MODE_DRAWING_SINGLE = 1;
   */
  MODE_DRAWING_SINGLE = 1,

  /**
   * 捏图双人
   *
   * @generated from enum value: MODE_DRAWING_MULTI = 2;
   */
  MODE_DRAWING_MULTI = 2,
}

/**
 * @generated from message step.raccoon.makephoto.CIPRole
 */
export declare class CIPRole extends Message<CIPRole> {
  /**
   * ip id string
   *
   * @generated from field: string cip = 1;
   */
  cip: string;

  /**
   * ip id int32
   *
   * @generated from field: int32 brand = 2;
   */
  brand: number;

  /**
   * 角色id
   *
   * @generated from field: string role = 3;
   */
  role: string;

  constructor(data?: PartialMessage<CIPRole>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.CIPRole";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CIPRole;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CIPRole;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CIPRole;

  static equals(a: CIPRole | PlainMessage<CIPRole> | undefined, b: CIPRole | PlainMessage<CIPRole> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.RichPhoto
 */
export declare class RichPhoto extends Message<RichPhoto> {
  /**
   * 基础图片信息
   *
   * @generated from field: step.raccoon.makephoto.RichPhoto.Photo photo = 1;
   */
  photo?: RichPhoto_Photo;

  /**
   * 图片生图原型信息
   *
   * @generated from field: step.raccoon.makephoto.RichPhoto.Proto proto = 2;
   */
  proto?: RichPhoto_Proto;

  /**
   * 生图模型相关信息
   *
   * @generated from field: step.raccoon.makephoto.RichPhoto.Reflux reflux = 3;
   */
  reflux?: RichPhoto_Reflux;

  constructor(data?: PartialMessage<RichPhoto>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.RichPhoto";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RichPhoto;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RichPhoto;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RichPhoto;

  static equals(a: RichPhoto | PlainMessage<RichPhoto> | undefined, b: RichPhoto | PlainMessage<RichPhoto> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.RichPhoto.Photo
 */
export declare class RichPhoto_Photo extends Message<RichPhoto_Photo> {
  /**
   * 捏图图片id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * 用户id
   *
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * 图片存储id
   *
   * @generated from field: string image_id = 3;
   */
  imageId: string;

  /**
   * 图片url
   *
   * @generated from field: string image_url = 4;
   */
  imageUrl: string;

  /**
   * 安全状态
   *
   * @generated from field: step.raccoon.common.CensoredState censored = 5;
   */
  censored: CensoredState;

  /**
   * 是否被删除
   *
   * @generated from field: bool deleted = 6;
   */
  deleted: boolean;

  /**
   * 生成时间
   *
   * @generated from field: string create_time = 7;
   */
  createTime: string;

  /**
   * 信息更新时间
   *
   * @generated from field: string update_time = 8;
   */
  updateTime: string;

  /**
   * 删除时间
   *
   * @generated from field: string delete_time = 9;
   */
  deleteTime: string;

  /**
   * 图片meta， json dict string
   *
   * @generated from field: string meta = 10;
   */
  meta: string;

  constructor(data?: PartialMessage<RichPhoto_Photo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.RichPhoto.Photo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RichPhoto_Photo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RichPhoto_Photo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RichPhoto_Photo;

  static equals(a: RichPhoto_Photo | PlainMessage<RichPhoto_Photo> | undefined, b: RichPhoto_Photo | PlainMessage<RichPhoto_Photo> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.RichPhoto.Proto
 */
export declare class RichPhoto_Proto extends Message<RichPhoto_Proto> {
  /**
   * 生图的原型id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * 生图角色列表
   *
   * @generated from field: repeated step.raccoon.makephoto.CIPRole roles = 3;
   */
  roles: CIPRole[];

  /**
   * 图片大小
   *
   * @generated from field: string size = 4;
   */
  size: string;

  /**
   * 风格
   *
   * @generated from field: string style = 5;
   */
  style: string;

  /**
   * 用户侧prompt
   *
   * @generated from field: string prompt = 6;
   */
  prompt: string;

  /**
   * 额外信息，主要是捏同款的参考信息
   *
   * @generated from field: string extra = 7;
   */
  extra: string;

  /**
   * 生图过程的trace url
   *
   * @generated from field: string trace_url = 8;
   */
  traceUrl: string;

  constructor(data?: PartialMessage<RichPhoto_Proto>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.RichPhoto.Proto";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RichPhoto_Proto;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RichPhoto_Proto;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RichPhoto_Proto;

  static equals(a: RichPhoto_Proto | PlainMessage<RichPhoto_Proto> | undefined, b: RichPhoto_Proto | PlainMessage<RichPhoto_Proto> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.RichPhoto.Reflux
 */
export declare class RichPhoto_Reflux extends Message<RichPhoto_Reflux> {
  /**
   * 图片的最终输入到模型的prompt
   *
   * @generated from field: string prompt = 1;
   */
  prompt: string;

  /**
   * 生图模型id
   *
   * @generated from field: string model = 2;
   */
  model: string;

  /**
   * 底模
   *
   * @generated from field: string base_model = 3;
   */
  baseModel: string;

  /**
   * 模型的prompt 格式化语句
   *
   * @generated from field: string prompt_format = 4;
   */
  promptFormat: string;

  /**
   * 生图实际的模型参数
   *
   * @generated from field: string params = 5;
   */
  params: string;

  constructor(data?: PartialMessage<RichPhoto_Reflux>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.RichPhoto.Reflux";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RichPhoto_Reflux;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RichPhoto_Reflux;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RichPhoto_Reflux;

  static equals(a: RichPhoto_Reflux | PlainMessage<RichPhoto_Reflux> | undefined, b: RichPhoto_Reflux | PlainMessage<RichPhoto_Reflux> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.QueryRichPhotoReq
 */
export declare class QueryRichPhotoReq extends Message<QueryRichPhotoReq> {
  /**
   * 用户uid
   *
   * @generated from field: string uid = 1;
   */
  uid: string;

  /**
   * 作品id
   *
   * @generated from field: string card_id = 2;
   */
  cardId: string;

  /**
   * 过滤用的安全级别，默认12-完全PASS，小于0时表示所有级别，具体值参考step.raccoon.common/state.proto;
   *
   * @generated from field: step.raccoon.common.CensoredState censored = 3;
   */
  censored: CensoredState;

  /**
   * 是否过滤掉用户删除掉的图片
   *
   * @generated from field: bool filter_deleted = 4;
   */
  filterDeleted: boolean;

  /**
   * 过滤用的图片生成时间范围
   *
   * @generated from field: step.raccoon.common.Timerange tr = 5;
   */
  tr?: Timerange;

  /**
   * 翻页用的参数
   *
   * @generated from field: step.raccoon.common.Pagination page = 6;
   */
  page?: Pagination;

  constructor(data?: PartialMessage<QueryRichPhotoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.QueryRichPhotoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryRichPhotoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryRichPhotoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryRichPhotoReq;

  static equals(a: QueryRichPhotoReq | PlainMessage<QueryRichPhotoReq> | undefined, b: QueryRichPhotoReq | PlainMessage<QueryRichPhotoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.QueryRichPhotoRsp
 */
export declare class QueryRichPhotoRsp extends Message<QueryRichPhotoRsp> {
  /**
   * @generated from field: repeated step.raccoon.makephoto.RichPhoto photos = 1;
   */
  photos: RichPhoto[];

  /**
   * @generated from field: step.raccoon.common.Pagination next_page = 2;
   */
  nextPage?: Pagination;

  constructor(data?: PartialMessage<QueryRichPhotoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.QueryRichPhotoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryRichPhotoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryRichPhotoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryRichPhotoRsp;

  static equals(a: QueryRichPhotoRsp | PlainMessage<QueryRichPhotoRsp> | undefined, b: QueryRichPhotoRsp | PlainMessage<QueryRichPhotoRsp> | undefined): boolean;
}

/**
 * 风格素材图片
 *
 * @generated from message step.raccoon.makephoto.StylePhoto
 */
export declare class StylePhoto extends Message<StylePhoto> {
  /**
   * 风格图片id
   *
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  /**
   * 风格类型，native/sref
   *
   * @generated from field: step.raccoon.common.StyleType style_type = 2;
   */
  styleType: StyleType;

  /**
   * 图片url
   *
   * @generated from field: string image_url = 3;
   */
  imageUrl: string;

  /**
   * 风格名
   *
   * @generated from field: string name = 4;
   */
  name: string;

  /**
   * 是否已上线
   *
   * @generated from field: bool is_online = 5;
   */
  isOnline: boolean;

  /**
   * 生成时间
   *
   * @generated from field: string create_time = 6;
   */
  createTime: string;

  /**
   * 信息更新时间
   *
   * @generated from field: string update_time = 7;
   */
  updateTime: string;

  /**
   * 删除时间
   *
   * @generated from field: string delete_time = 8;
   */
  deleteTime: string;

  /**
   * 图片meta， json dict string
   *
   * @generated from field: string meta = 9;
   */
  meta: string;

  /**
   * 图片额外信息json 字典
   *
   * @generated from field: string extra = 10;
   */
  extra: string;

  /**
   * @generated from field: string image_id = 11;
   */
  imageId: string;

  /**
   * @generated from field: int64 use_counts = 12;
   */
  useCounts: bigint;

  /**
   * 风格算法侧id
   *
   * @generated from field: string name_en = 13;
   */
  nameEn: string;

  constructor(data?: PartialMessage<StylePhoto>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.StylePhoto";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StylePhoto;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StylePhoto;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StylePhoto;

  static equals(a: StylePhoto | PlainMessage<StylePhoto> | undefined, b: StylePhoto | PlainMessage<StylePhoto> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.ImportStylePhotoReq
 */
export declare class ImportStylePhotoReq extends Message<ImportStylePhotoReq> {
  /**
   * 图片在media service的图片id
   *
   * @generated from field: string image_id = 1;
   */
  imageId: string;

  /**
   * 风格名
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * 风格类型，native/sref
   *
   * @generated from field: step.raccoon.common.StyleType style_type = 3;
   */
  styleType: StyleType;

  /**
   * 风格英文id
   *
   * @generated from field: string name_en = 4;
   */
  nameEn: string;

  constructor(data?: PartialMessage<ImportStylePhotoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.ImportStylePhotoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImportStylePhotoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImportStylePhotoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImportStylePhotoReq;

  static equals(a: ImportStylePhotoReq | PlainMessage<ImportStylePhotoReq> | undefined, b: ImportStylePhotoReq | PlainMessage<ImportStylePhotoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.ImportStylePhotoRsp
 */
export declare class ImportStylePhotoRsp extends Message<ImportStylePhotoRsp> {
  /**
   * @generated from field: step.raccoon.makephoto.StylePhoto photo = 1;
   */
  photo?: StylePhoto;

  constructor(data?: PartialMessage<ImportStylePhotoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.ImportStylePhotoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImportStylePhotoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImportStylePhotoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImportStylePhotoRsp;

  static equals(a: ImportStylePhotoRsp | PlainMessage<ImportStylePhotoRsp> | undefined, b: ImportStylePhotoRsp | PlainMessage<ImportStylePhotoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.UpdateStylePhotoReq
 */
export declare class UpdateStylePhotoReq extends Message<UpdateStylePhotoReq> {
  /**
   * 风格图片id
   *
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  /**
   * 图片在media service的图片id
   *
   * @generated from field: string image_id = 2;
   */
  imageId: string;

  /**
   * 风格名
   *
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * 风格类型
   *
   * @generated from field: step.raccoon.common.StyleType style_type = 4;
   */
  styleType: StyleType;

  /**
   * 风格算法侧英文id
   *
   * @generated from field: string name_en = 5;
   */
  nameEn: string;

  constructor(data?: PartialMessage<UpdateStylePhotoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.UpdateStylePhotoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateStylePhotoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateStylePhotoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateStylePhotoReq;

  static equals(a: UpdateStylePhotoReq | PlainMessage<UpdateStylePhotoReq> | undefined, b: UpdateStylePhotoReq | PlainMessage<UpdateStylePhotoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.UpdateStylePhotoRsp
 */
export declare class UpdateStylePhotoRsp extends Message<UpdateStylePhotoRsp> {
  /**
   * @generated from field: step.raccoon.makephoto.StylePhoto photo = 1;
   */
  photo?: StylePhoto;

  constructor(data?: PartialMessage<UpdateStylePhotoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.UpdateStylePhotoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateStylePhotoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateStylePhotoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateStylePhotoRsp;

  static equals(a: UpdateStylePhotoRsp | PlainMessage<UpdateStylePhotoRsp> | undefined, b: UpdateStylePhotoRsp | PlainMessage<UpdateStylePhotoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.QueryStylePhotosReq
 */
export declare class QueryStylePhotosReq extends Message<QueryStylePhotosReq> {
  /**
   * 名称过滤查询，模糊匹配
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * 上线状态过滤，不指定默认返回所有状态
   *
   * @generated from field: optional bool online = 2;
   */
  online?: boolean;

  /**
   * 过滤风格类型，预留
   *
   * @generated from field: repeated step.raccoon.common.StyleType style_types = 3;
   */
  styleTypes: StyleType[];

  /**
   * 排序，支持"create_time", "use_count", "delete_state"
   *
   * @generated from field: repeated step.raccoon.common.QueryOrderItem order = 4;
   */
  order: QueryOrderItem[];

  /**
   * 分页size不超过64
   *
   * @generated from field: step.raccoon.common.Pagination page = 5;
   */
  page?: Pagination;

  constructor(data?: PartialMessage<QueryStylePhotosReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.QueryStylePhotosReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryStylePhotosReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryStylePhotosReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryStylePhotosReq;

  static equals(a: QueryStylePhotosReq | PlainMessage<QueryStylePhotosReq> | undefined, b: QueryStylePhotosReq | PlainMessage<QueryStylePhotosReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.QueryStylePhotosRsp
 */
export declare class QueryStylePhotosRsp extends Message<QueryStylePhotosRsp> {
  /**
   * @generated from field: repeated step.raccoon.makephoto.StylePhoto photos = 1;
   */
  photos: StylePhoto[];

  /**
   * @generated from field: step.raccoon.common.Pagination next_page = 2;
   */
  nextPage?: Pagination;

  constructor(data?: PartialMessage<QueryStylePhotosRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.QueryStylePhotosRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryStylePhotosRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryStylePhotosRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryStylePhotosRsp;

  static equals(a: QueryStylePhotosRsp | PlainMessage<QueryStylePhotosRsp> | undefined, b: QueryStylePhotosRsp | PlainMessage<QueryStylePhotosRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.OnlineStylePhotoReq
 */
export declare class OnlineStylePhotoReq extends Message<OnlineStylePhotoReq> {
  /**
   * 上线的id
   *
   * @generated from field: repeated string photo_ids = 1;
   */
  photoIds: string[];

  /**
   * 1上线/ 0下线
   *
   * @generated from field: bool online = 2;
   */
  online: boolean;

  constructor(data?: PartialMessage<OnlineStylePhotoReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.OnlineStylePhotoReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnlineStylePhotoReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnlineStylePhotoReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnlineStylePhotoReq;

  static equals(a: OnlineStylePhotoReq | PlainMessage<OnlineStylePhotoReq> | undefined, b: OnlineStylePhotoReq | PlainMessage<OnlineStylePhotoReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.OnlineStylePhotoRsp
 */
export declare class OnlineStylePhotoRsp extends Message<OnlineStylePhotoRsp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<OnlineStylePhotoRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.OnlineStylePhotoRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnlineStylePhotoRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnlineStylePhotoRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnlineStylePhotoRsp;

  static equals(a: OnlineStylePhotoRsp | PlainMessage<OnlineStylePhotoRsp> | undefined, b: OnlineStylePhotoRsp | PlainMessage<OnlineStylePhotoRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.UpdateStyleConfigReq
 */
export declare class UpdateStyleConfigReq extends Message<UpdateStyleConfigReq> {
  /**
   * 风格图片id列表
   *
   * @generated from field: repeated string photo_ids = 1;
   */
  photoIds: string[];

  /**
   * 业务模式
   *
   * @generated from field: step.raccoon.makephoto.GameStyleMode mode = 2;
   */
  mode: GameStyleMode;

  /**
   * 操作人
   *
   * @generated from field: string operator = 3;
   */
  operator: string;

  constructor(data?: PartialMessage<UpdateStyleConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.UpdateStyleConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateStyleConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateStyleConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateStyleConfigReq;

  static equals(a: UpdateStyleConfigReq | PlainMessage<UpdateStyleConfigReq> | undefined, b: UpdateStyleConfigReq | PlainMessage<UpdateStyleConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.UpdateStyleConfigRsp
 */
export declare class UpdateStyleConfigRsp extends Message<UpdateStyleConfigRsp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<UpdateStyleConfigRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.UpdateStyleConfigRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateStyleConfigRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateStyleConfigRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateStyleConfigRsp;

  static equals(a: UpdateStyleConfigRsp | PlainMessage<UpdateStyleConfigRsp> | undefined, b: UpdateStyleConfigRsp | PlainMessage<UpdateStyleConfigRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.GetStyleConfigReq
 */
export declare class GetStyleConfigReq extends Message<GetStyleConfigReq> {
  /**
   * @generated from field: step.raccoon.makephoto.GameStyleMode mode = 1;
   */
  mode: GameStyleMode;

  /**
   * 分页size不要超过64
   *
   * @generated from field: step.raccoon.common.Pagination page = 2;
   */
  page?: Pagination;

  constructor(data?: PartialMessage<GetStyleConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.GetStyleConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetStyleConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetStyleConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetStyleConfigReq;

  static equals(a: GetStyleConfigReq | PlainMessage<GetStyleConfigReq> | undefined, b: GetStyleConfigReq | PlainMessage<GetStyleConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.StyleItem
 */
export declare class StyleItem extends Message<StyleItem> {
  /**
   * 风格id
   *
   * @generated from field: string photo_id = 1;
   */
  photoId: string;

  /**
   * 图片url
   *
   * @generated from field: string image_url = 2;
   */
  imageUrl: string;

  /**
   * 名称
   *
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * 是否已下线
   *
   * @generated from field: bool online = 4;
   */
  online: boolean;

  /**
   * @generated from field: string image_id = 5;
   */
  imageId: string;

  constructor(data?: PartialMessage<StyleItem>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.StyleItem";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StyleItem;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StyleItem;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StyleItem;

  static equals(a: StyleItem | PlainMessage<StyleItem> | undefined, b: StyleItem | PlainMessage<StyleItem> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.GetStyleConfigRsp
 */
export declare class GetStyleConfigRsp extends Message<GetStyleConfigRsp> {
  /**
   * @generated from field: repeated step.raccoon.makephoto.StyleItem items = 1;
   */
  items: StyleItem[];

  /**
   * @generated from field: step.raccoon.common.Pagination next_page = 2;
   */
  nextPage?: Pagination;

  constructor(data?: PartialMessage<GetStyleConfigRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.GetStyleConfigRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetStyleConfigRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetStyleConfigRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetStyleConfigRsp;

  static equals(a: GetStyleConfigRsp | PlainMessage<GetStyleConfigRsp> | undefined, b: GetStyleConfigRsp | PlainMessage<GetStyleConfigRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.PreviewStyleConfigReq
 */
export declare class PreviewStyleConfigReq extends Message<PreviewStyleConfigReq> {
  /**
   * 场景
   *
   * @generated from field: step.raccoon.makephoto.GameStyleMode mode = 1;
   */
  mode: GameStyleMode;

  /**
   * @generated from field: repeated string photo_ids = 2;
   */
  photoIds: string[];

  /**
   * 分页size不要超过64
   *
   * @generated from field: step.raccoon.common.PagePagination page = 3;
   */
  page?: PagePagination;

  constructor(data?: PartialMessage<PreviewStyleConfigReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.PreviewStyleConfigReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PreviewStyleConfigReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PreviewStyleConfigReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PreviewStyleConfigReq;

  static equals(a: PreviewStyleConfigReq | PlainMessage<PreviewStyleConfigReq> | undefined, b: PreviewStyleConfigReq | PlainMessage<PreviewStyleConfigReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.PreviewStyleConfigRsp
 */
export declare class PreviewStyleConfigRsp extends Message<PreviewStyleConfigRsp> {
  /**
   * @generated from field: repeated step.raccoon.makephoto.StyleItem items = 1;
   */
  items: StyleItem[];

  /**
   * 总数量
   *
   * @generated from field: int64 total = 2;
   */
  total: bigint;

  constructor(data?: PartialMessage<PreviewStyleConfigRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.PreviewStyleConfigRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PreviewStyleConfigRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PreviewStyleConfigRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PreviewStyleConfigRsp;

  static equals(a: PreviewStyleConfigRsp | PlainMessage<PreviewStyleConfigRsp> | undefined, b: PreviewStyleConfigRsp | PlainMessage<PreviewStyleConfigRsp> | undefined): boolean;
}

/**
 * 炖图tab
 *
 * @generated from message step.raccoon.makephoto.FeatureType
 */
export declare class FeatureType extends Message<FeatureType> {
  /**
   * 炖图tabID
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * 炖图tab名称
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * 创建时间
   *
   * @generated from field: string create_time = 3;
   */
  createTime: string;

  /**
   * 最近更新时间
   *
   * @generated from field: string update_time = 4;
   */
  updateTime: string;

  /**
   * 删除时间
   *
   * @generated from field: string delete_time = 5;
   */
  deleteTime: string;

  /**
   * Tab图标链接
   *
   * @generated from field: string image_url = 6;
   */
  imageUrl: string;

  /**
   * 上线状态：1-上线；0-下线
   *
   * @generated from field: int32 online_state = 7;
   */
  onlineState: number;

  /**
   * 权重排序
   *
   * @generated from field: int64 order_weight = 8;
   */
  orderWeight: bigint;

  /**
   * 炖图tab中文名称
   *
   * @generated from field: string name_cn = 9;
   */
  nameCn: string;

  /**
   * 灵感词数
   *
   * @generated from field: int64 feature_num = 10;
   */
  featureNum: bigint;

  constructor(data?: PartialMessage<FeatureType>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.FeatureType";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FeatureType;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FeatureType;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FeatureType;

  static equals(a: FeatureType | PlainMessage<FeatureType> | undefined, b: FeatureType | PlainMessage<FeatureType> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.ImportFeatureTypeReq
 */
export declare class ImportFeatureTypeReq extends Message<ImportFeatureTypeReq> {
  /**
   * 炖图tab, 必填且必须为：expression/action/scene/artist/cloth/shot/addition之一
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * 炖图tab 中文名
   *
   * @generated from field: string name_cn = 2;
   */
  nameCn: string;

  /**
   * 图片在media service的url
   *
   * @generated from field: string image_url = 3;
   */
  imageUrl: string;

  /**
   * 排序权重（显示顺序）
   *
   * @generated from field: int64 order_weight = 4;
   */
  orderWeight: bigint;

  constructor(data?: PartialMessage<ImportFeatureTypeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.ImportFeatureTypeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImportFeatureTypeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImportFeatureTypeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImportFeatureTypeReq;

  static equals(a: ImportFeatureTypeReq | PlainMessage<ImportFeatureTypeReq> | undefined, b: ImportFeatureTypeReq | PlainMessage<ImportFeatureTypeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.ImportFeatureTypeRsp
 */
export declare class ImportFeatureTypeRsp extends Message<ImportFeatureTypeRsp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<ImportFeatureTypeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.ImportFeatureTypeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImportFeatureTypeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImportFeatureTypeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImportFeatureTypeRsp;

  static equals(a: ImportFeatureTypeRsp | PlainMessage<ImportFeatureTypeRsp> | undefined, b: ImportFeatureTypeRsp | PlainMessage<ImportFeatureTypeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.UpdateFeatureTypeReq
 */
export declare class UpdateFeatureTypeReq extends Message<UpdateFeatureTypeReq> {
  /**
   * featuretype id，创建后的tab id
   *
   * @generated from field: int64 featuretype_id = 1;
   */
  featuretypeId: bigint;

  /**
   * 炖图tab
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * 炖图tab 中文名
   *
   * @generated from field: string name_cn = 3;
   */
  nameCn: string;

  /**
   * 图片在media service的url
   *
   * @generated from field: string image_url = 4;
   */
  imageUrl: string;

  /**
   * 排序权重（显示顺序）
   *
   * @generated from field: int64 order_weight = 5;
   */
  orderWeight: bigint;

  constructor(data?: PartialMessage<UpdateFeatureTypeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.UpdateFeatureTypeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateFeatureTypeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateFeatureTypeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateFeatureTypeReq;

  static equals(a: UpdateFeatureTypeReq | PlainMessage<UpdateFeatureTypeReq> | undefined, b: UpdateFeatureTypeReq | PlainMessage<UpdateFeatureTypeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.UpdateFeatureTypeRsp
 */
export declare class UpdateFeatureTypeRsp extends Message<UpdateFeatureTypeRsp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<UpdateFeatureTypeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.UpdateFeatureTypeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateFeatureTypeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateFeatureTypeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateFeatureTypeRsp;

  static equals(a: UpdateFeatureTypeRsp | PlainMessage<UpdateFeatureTypeRsp> | undefined, b: UpdateFeatureTypeRsp | PlainMessage<UpdateFeatureTypeRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.OnlineFeatureTypeReq
 */
export declare class OnlineFeatureTypeReq extends Message<OnlineFeatureTypeReq> {
  /**
   * 上线的id，支持批量处理
   *
   * @generated from field: repeated int64 featuretype_ids = 1;
   */
  featuretypeIds: bigint[];

  /**
   * 1 上线/  0下线
   *
   * @generated from field: int32 online = 2;
   */
  online: number;

  constructor(data?: PartialMessage<OnlineFeatureTypeReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.OnlineFeatureTypeReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnlineFeatureTypeReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnlineFeatureTypeReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnlineFeatureTypeReq;

  static equals(a: OnlineFeatureTypeReq | PlainMessage<OnlineFeatureTypeReq> | undefined, b: OnlineFeatureTypeReq | PlainMessage<OnlineFeatureTypeReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.OnlineFeatureTypeRsp
 */
export declare class OnlineFeatureTypeRsp extends Message<OnlineFeatureTypeRsp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<OnlineFeatureTypeRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.OnlineFeatureTypeRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnlineFeatureTypeRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnlineFeatureTypeRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnlineFeatureTypeRsp;

  static equals(a: OnlineFeatureTypeRsp | PlainMessage<OnlineFeatureTypeRsp> | undefined, b: OnlineFeatureTypeRsp | PlainMessage<OnlineFeatureTypeRsp> | undefined): boolean;
}

/**
 * 查询全部，炖图tab不会太多
 *
 * @generated from message step.raccoon.makephoto.QueryFeatureTypesReq
 */
export declare class QueryFeatureTypesReq extends Message<QueryFeatureTypesReq> {
  constructor(data?: PartialMessage<QueryFeatureTypesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.QueryFeatureTypesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryFeatureTypesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryFeatureTypesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryFeatureTypesReq;

  static equals(a: QueryFeatureTypesReq | PlainMessage<QueryFeatureTypesReq> | undefined, b: QueryFeatureTypesReq | PlainMessage<QueryFeatureTypesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.QueryFeatureTypesRsp
 */
export declare class QueryFeatureTypesRsp extends Message<QueryFeatureTypesRsp> {
  /**
   * @generated from field: repeated step.raccoon.makephoto.FeatureType featuretypes = 1;
   */
  featuretypes: FeatureType[];

  constructor(data?: PartialMessage<QueryFeatureTypesRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.QueryFeatureTypesRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryFeatureTypesRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryFeatureTypesRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryFeatureTypesRsp;

  static equals(a: QueryFeatureTypesRsp | PlainMessage<QueryFeatureTypesRsp> | undefined, b: QueryFeatureTypesRsp | PlainMessage<QueryFeatureTypesRsp> | undefined): boolean;
}

/**
 * 灵感词
 *
 * @generated from message step.raccoon.makephoto.Feature
 */
export declare class Feature extends Message<Feature> {
  /**
   * 炖图分类ID
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * 灵感词名称
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * 炖图tab名称
   *
   * @generated from field: string featuretype_name = 3;
   */
  featuretypeName: string;

  /**
   * 炖图tabID
   *
   * @generated from field: int64 featuretype_id = 4;
   */
  featuretypeId: bigint;

  /**
   * 创建时间
   *
   * @generated from field: string create_time = 5;
   */
  createTime: string;

  /**
   * 最近更新时间
   *
   * @generated from field: string update_time = 6;
   */
  updateTime: string;

  /**
   * 删除时间
   *
   * @generated from field: string delete_time = 7;
   */
  deleteTime: string;

  /**
   * 灵感词图标链接
   *
   * @generated from field: string image_url = 8;
   */
  imageUrl: string;

  /**
   * 上线状态：1-上线；0-下线
   *
   * @generated from field: int32 online_state = 9;
   */
  onlineState: number;

  /**
   * 权重排序
   *
   * @generated from field: int64 order_weight = 10;
   */
  orderWeight: bigint;

  /**
   * prompt
   *
   * @generated from field: string prompt = 11;
   */
  prompt: string;

  /**
   * 灵感词中文名称
   *
   * @generated from field: string name_cn = 12;
   */
  nameCn: string;

  constructor(data?: PartialMessage<Feature>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.Feature";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Feature;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Feature;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Feature;

  static equals(a: Feature | PlainMessage<Feature> | undefined, b: Feature | PlainMessage<Feature> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.ImportFeatureReq
 */
export declare class ImportFeatureReq extends Message<ImportFeatureReq> {
  /**
   * 灵感词名称
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * 灵感词中文名称
   *
   * @generated from field: string name_cn = 2;
   */
  nameCn: string;

  /**
   * 炖图tab名称
   *
   * @generated from field: string featuretype_name = 3;
   */
  featuretypeName: string;

  /**
   * prompt
   *
   * @generated from field: string prompt = 4;
   */
  prompt: string;

  /**
   * 权重排序
   *
   * @generated from field: int64 order_weight = 5;
   */
  orderWeight: bigint;

  /**
   * 灵感词图标链接
   *
   * @generated from field: string image_url = 6;
   */
  imageUrl: string;

  /**
   * 炖图tab id
   *
   * @generated from field: int64 featuretype_id = 7;
   */
  featuretypeId: bigint;

  constructor(data?: PartialMessage<ImportFeatureReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.ImportFeatureReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImportFeatureReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImportFeatureReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImportFeatureReq;

  static equals(a: ImportFeatureReq | PlainMessage<ImportFeatureReq> | undefined, b: ImportFeatureReq | PlainMessage<ImportFeatureReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.ImportFeaturesReq
 */
export declare class ImportFeaturesReq extends Message<ImportFeaturesReq> {
  /**
   * 炖图tab名称
   *
   * @generated from field: string featuretype_name = 1;
   */
  featuretypeName: string;

  /**
   * 炖图tab id
   *
   * @generated from field: int64 featuretype_id = 2;
   */
  featuretypeId: bigint;

  /**
   * csv_url
   *
   * @generated from field: string csv_url = 3;
   */
  csvUrl: string;

  constructor(data?: PartialMessage<ImportFeaturesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.ImportFeaturesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImportFeaturesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImportFeaturesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImportFeaturesReq;

  static equals(a: ImportFeaturesReq | PlainMessage<ImportFeaturesReq> | undefined, b: ImportFeaturesReq | PlainMessage<ImportFeaturesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.UpdateFeatureReq
 */
export declare class UpdateFeatureReq extends Message<UpdateFeatureReq> {
  /**
   * feature id
   *
   * @generated from field: int64 feature_id = 1;
   */
  featureId: bigint;

  /**
   * 灵感词名称
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string name_cn = 3;
   */
  nameCn: string;

  /**
   * 炖图tab名称
   *
   * @generated from field: string featuretype_name = 4;
   */
  featuretypeName: string;

  /**
   * prompt
   *
   * @generated from field: string prompt = 5;
   */
  prompt: string;

  /**
   * 权重排序
   *
   * @generated from field: int64 order_weight = 6;
   */
  orderWeight: bigint;

  /**
   * 灵感词图标链接
   *
   * @generated from field: string image_url = 7;
   */
  imageUrl: string;

  /**
   * 炖图tab id
   *
   * @generated from field: int64 featuretype_id = 8;
   */
  featuretypeId: bigint;

  constructor(data?: PartialMessage<UpdateFeatureReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.UpdateFeatureReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateFeatureReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateFeatureReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateFeatureReq;

  static equals(a: UpdateFeatureReq | PlainMessage<UpdateFeatureReq> | undefined, b: UpdateFeatureReq | PlainMessage<UpdateFeatureReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.OnlineFeatureReq
 */
export declare class OnlineFeatureReq extends Message<OnlineFeatureReq> {
  /**
   * 上线的id，支持批量处理
   *
   * @generated from field: repeated int64 feature_ids = 1;
   */
  featureIds: bigint[];

  /**
   * 1上线/ 0下线
   *
   * @generated from field: int32 online = 2;
   */
  online: number;

  constructor(data?: PartialMessage<OnlineFeatureReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.OnlineFeatureReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnlineFeatureReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnlineFeatureReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnlineFeatureReq;

  static equals(a: OnlineFeatureReq | PlainMessage<OnlineFeatureReq> | undefined, b: OnlineFeatureReq | PlainMessage<OnlineFeatureReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.QueryFeaturesReq
 */
export declare class QueryFeaturesReq extends Message<QueryFeaturesReq> {
  /**
   * 名称过滤查询，模糊匹配，中文
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * 名称过滤查询，模糊匹配，中文，expression/cloth/shot/scene/action/artist
   *
   * @generated from field: string featuretype_name = 2;
   */
  featuretypeName: string;

  /**
   * 上线状态过滤，不指定默认返回所有状态
   *
   * @generated from field: optional bool online = 3;
   */
  online?: boolean;

  /**
   * 分页size不超过64
   *
   * @generated from field: step.raccoon.common.Pagination page = 4;
   */
  page?: Pagination;

  constructor(data?: PartialMessage<QueryFeaturesReq>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.QueryFeaturesReq";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryFeaturesReq;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryFeaturesReq;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryFeaturesReq;

  static equals(a: QueryFeaturesReq | PlainMessage<QueryFeaturesReq> | undefined, b: QueryFeaturesReq | PlainMessage<QueryFeaturesReq> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.ImportFeatureRsp
 */
export declare class ImportFeatureRsp extends Message<ImportFeatureRsp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<ImportFeatureRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.ImportFeatureRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImportFeatureRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImportFeatureRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImportFeatureRsp;

  static equals(a: ImportFeatureRsp | PlainMessage<ImportFeatureRsp> | undefined, b: ImportFeatureRsp | PlainMessage<ImportFeatureRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.UpdateFeatureRsp
 */
export declare class UpdateFeatureRsp extends Message<UpdateFeatureRsp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<UpdateFeatureRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.UpdateFeatureRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateFeatureRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateFeatureRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateFeatureRsp;

  static equals(a: UpdateFeatureRsp | PlainMessage<UpdateFeatureRsp> | undefined, b: UpdateFeatureRsp | PlainMessage<UpdateFeatureRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.OnlineFeatureRsp
 */
export declare class OnlineFeatureRsp extends Message<OnlineFeatureRsp> {
  /**
   * @generated from field: bool ok = 1;
   */
  ok: boolean;

  constructor(data?: PartialMessage<OnlineFeatureRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.OnlineFeatureRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnlineFeatureRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnlineFeatureRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnlineFeatureRsp;

  static equals(a: OnlineFeatureRsp | PlainMessage<OnlineFeatureRsp> | undefined, b: OnlineFeatureRsp | PlainMessage<OnlineFeatureRsp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.makephoto.QueryFeaturesRsp
 */
export declare class QueryFeaturesRsp extends Message<QueryFeaturesRsp> {
  /**
   * @generated from field: repeated step.raccoon.makephoto.Feature features = 1;
   */
  features: Feature[];

  /**
   * @generated from field: step.raccoon.common.Pagination next_page = 2;
   */
  nextPage?: Pagination;

  constructor(data?: PartialMessage<QueryFeaturesRsp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.makephoto.QueryFeaturesRsp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QueryFeaturesRsp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QueryFeaturesRsp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QueryFeaturesRsp;

  static equals(a: QueryFeaturesRsp | PlainMessage<QueryFeaturesRsp> | undefined, b: QueryFeaturesRsp | PlainMessage<QueryFeaturesRsp> | undefined): boolean;
}

