// @generated by protoc-gen-es v1.8.0 with parameter "target=js+dts"
// @generated from file raccoon/creatoractivity/activity_admin.proto (package step.raccoon.creatoractivity, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { RewardType } from "../common/types_pb.js";
import type { Pagination } from "../common/utils_pb.js";

/**
 * 规则类型，影响数据加载
 *
 * @generated from enum step.raccoon.creatoractivity.RuleType
 */
export declare enum RuleType {
  /**
   * @generated from enum value: UNKONWN_RULE = 0;
   */
  UNKONWN_RULE = 0,

  /**
   * @generated from enum value: PHOTO = 1;
   */
  PHOTO = 1,
}

/**
 * @generated from message step.raccoon.creatoractivity.RewardItem
 */
export declare class RewardItem extends Message<RewardItem> {
  /**
   * @generated from field: step.raccoon.common.RewardType rewardType = 1;
   */
  rewardType: RewardType;

  /**
   * @generated from field: int32 amount = 2;
   */
  amount: number;

  /**
   * @generated from field: string uid = 3;
   */
  uid: string;

  /**
   * @generated from field: string cdkey = 4;
   */
  cdkey: string;

  /**
   * 0表示发送成功，-1表示发送失败
   *
   * @generated from field: int32 state = 5;
   */
  state: number;

  /**
   * 创建时间
   *
   * @generated from field: string create_time = 6;
   */
  createTime: string;

  /**
   * 创建者
   *
   * @generated from field: string creator = 7;
   */
  creator: string;

  constructor(data?: PartialMessage<RewardItem>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.RewardItem";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardItem;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardItem;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardItem;

  static equals(a: RewardItem | PlainMessage<RewardItem> | undefined, b: RewardItem | PlainMessage<RewardItem> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.DeliverRewardRequest
 */
export declare class DeliverRewardRequest extends Message<DeliverRewardRequest> {
  /**
   * @generated from field: repeated step.raccoon.creatoractivity.RewardItem rewardItem = 1;
   */
  rewardItem: RewardItem[];

  /**
   * @generated from field: string activity_id = 2;
   */
  activityId: string;

  /**
   * @generated from field: step.raccoon.creatoractivity.RewardNotice reward_notice = 3;
   */
  rewardNotice?: RewardNotice;

  constructor(data?: PartialMessage<DeliverRewardRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.DeliverRewardRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeliverRewardRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeliverRewardRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeliverRewardRequest;

  static equals(a: DeliverRewardRequest | PlainMessage<DeliverRewardRequest> | undefined, b: DeliverRewardRequest | PlainMessage<DeliverRewardRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.RewardNotice
 */
export declare class RewardNotice extends Message<RewardNotice> {
  /**
   * @generated from field: string inboxTitle = 1;
   */
  inboxTitle: string;

  /**
   * 发放京东卡不需要站内信内容，会自动根据cdkey拼接
   *
   * @generated from field: string inboxContent = 2;
   */
  inboxContent: string;

  /**
   * @generated from field: string inboxCover = 3;
   */
  inboxCover: string;

  constructor(data?: PartialMessage<RewardNotice>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.RewardNotice";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardNotice;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardNotice;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardNotice;

  static equals(a: RewardNotice | PlainMessage<RewardNotice> | undefined, b: RewardNotice | PlainMessage<RewardNotice> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.RewardListParam
 */
export declare class RewardListParam extends Message<RewardListParam> {
  constructor(data?: PartialMessage<RewardListParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.RewardListParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardListParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardListParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardListParam;

  static equals(a: RewardListParam | PlainMessage<RewardListParam> | undefined, b: RewardListParam | PlainMessage<RewardListParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.RewardListRequest
 */
export declare class RewardListRequest extends Message<RewardListRequest> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  /**
   * @generated from field: step.raccoon.creatoractivity.RewardListParam search = 2;
   */
  search?: RewardListParam;

  constructor(data?: PartialMessage<RewardListRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.RewardListRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardListRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardListRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardListRequest;

  static equals(a: RewardListRequest | PlainMessage<RewardListRequest> | undefined, b: RewardListRequest | PlainMessage<RewardListRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.RewardListResp
 */
export declare class RewardListResp extends Message<RewardListResp> {
  /**
   * @generated from field: repeated step.raccoon.creatoractivity.RewardItem rewardItems = 1;
   */
  rewardItems: RewardItem[];

  /**
   * @generated from field: string next_cursor = 2;
   */
  nextCursor: string;

  constructor(data?: PartialMessage<RewardListResp>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.RewardListResp";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RewardListResp;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RewardListResp;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RewardListResp;

  static equals(a: RewardListResp | PlainMessage<RewardListResp> | undefined, b: RewardListResp | PlainMessage<RewardListResp> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.UpdateActivityConfRequest
 */
export declare class UpdateActivityConfRequest extends Message<UpdateActivityConfRequest> {
  /**
   * @generated from field: string activity_id = 1;
   */
  activityId: string;

  /**
   * @generated from field: int32 type = 2;
   */
  type: number;

  /**
   * @generated from field: string conf = 3;
   */
  conf: string;

  constructor(data?: PartialMessage<UpdateActivityConfRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.UpdateActivityConfRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateActivityConfRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateActivityConfRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateActivityConfRequest;

  static equals(a: UpdateActivityConfRequest | PlainMessage<UpdateActivityConfRequest> | undefined, b: UpdateActivityConfRequest | PlainMessage<UpdateActivityConfRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.AdminActivity
 */
export declare class AdminActivity extends Message<AdminActivity> {
  /**
   * @generated from field: string activity_id = 1;
   */
  activityId: string;

  /**
   * @generated from field: string activity_name = 2;
   */
  activityName: string;

  /**
   * @generated from field: string topic_id = 3;
   */
  topicId: string;

  /**
   * @generated from field: repeated int32 game_types = 4;
   */
  gameTypes: number[];

  /**
   * @generated from field: string start_time = 5;
   */
  startTime: string;

  /**
   * @generated from field: string end_time = 6;
   */
  endTime: string;

  /**
   * @generated from field: bool needReward = 7;
   */
  needReward: boolean;

  /**
   * 奖励配置
   *
   * @generated from field: step.raccoon.creatoractivity.AdminRewardConf reward_conf = 8;
   */
  rewardConf?: AdminRewardConf;

  /**
   * 是否获奖规则
   *
   * @generated from field: repeated step.raccoon.creatoractivity.AdminRule rules = 9;
   */
  rules: AdminRule[];

  /**
   * @generated from field: int32 priority = 10;
   */
  priority: number;

  /**
   * @generated from field: string creator = 100;
   */
  creator: string;

  /**
   * @generated from field: string created_at = 101;
   */
  createdAt: string;

  /**
   * @generated from field: string updator = 102;
   */
  updator: string;

  /**
   * @generated from field: string updated_at = 103;
   */
  updatedAt: string;

  constructor(data?: PartialMessage<AdminActivity>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.AdminActivity";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminActivity;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminActivity;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminActivity;

  static equals(a: AdminActivity | PlainMessage<AdminActivity> | undefined, b: AdminActivity | PlainMessage<AdminActivity> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.AdminRewardConf
 */
export declare class AdminRewardConf extends Message<AdminRewardConf> {
  /**
   * @generated from field: repeated step.raccoon.creatoractivity.AdminRewardGradient gradient = 1;
   */
  gradient: AdminRewardGradient[];

  /**
   * 每天可领取次数上限
   *
   * @generated from field: int32 reward_limit = 2;
   */
  rewardLimit: number;

  /**
   * 活动累计可领取次数上限
   *
   * @generated from field: int32 total_reward_limit = 3;
   */
  totalRewardLimit: number;

  /**
   * 每月可领取次数上限
   *
   * @generated from field: int32 month_reward_limit = 4;
   */
  monthRewardLimit: number;

  /**
   * 每天可领取积分上限
   *
   * @generated from field: int32 reward_amount_limit = 5;
   */
  rewardAmountLimit: number;

  /**
   * 活动累计可领取积分上限
   *
   * @generated from field: int32 total_reward_amount_limit = 6;
   */
  totalRewardAmountLimit: number;

  /**
   * 每月可领取积分上限
   *
   * @generated from field: int32 month_reward_amount_limit = 7;
   */
  monthRewardAmountLimit: number;

  constructor(data?: PartialMessage<AdminRewardConf>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.AdminRewardConf";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminRewardConf;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminRewardConf;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminRewardConf;

  static equals(a: AdminRewardConf | PlainMessage<AdminRewardConf> | undefined, b: AdminRewardConf | PlainMessage<AdminRewardConf> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.AdminRewardGradient
 */
export declare class AdminRewardGradient extends Message<AdminRewardGradient> {
  /**
   * 奖励
   *
   * @generated from field: int32 reward = 1;
   */
  reward: number;

  /**
   * 概率, 百分比
   *
   * @generated from field: int32 probability = 2;
   */
  probability: number;

  constructor(data?: PartialMessage<AdminRewardGradient>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.AdminRewardGradient";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminRewardGradient;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminRewardGradient;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminRewardGradient;

  static equals(a: AdminRewardGradient | PlainMessage<AdminRewardGradient> | undefined, b: AdminRewardGradient | PlainMessage<AdminRewardGradient> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.AdminRule
 */
export declare class AdminRule extends Message<AdminRule> {
  /**
   * @generated from field: step.raccoon.creatoractivity.RuleType rule_type = 1;
   */
  ruleType: RuleType;

  /**
   * @generated from field: string rule = 2;
   */
  rule: string;

  constructor(data?: PartialMessage<AdminRule>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.AdminRule";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AdminRule;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AdminRule;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AdminRule;

  static equals(a: AdminRule | PlainMessage<AdminRule> | undefined, b: AdminRule | PlainMessage<AdminRule> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.CreateActivityRequest
 */
export declare class CreateActivityRequest extends Message<CreateActivityRequest> {
  /**
   * @generated from field: step.raccoon.creatoractivity.AdminActivity activity = 1;
   */
  activity?: AdminActivity;

  constructor(data?: PartialMessage<CreateActivityRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.CreateActivityRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateActivityRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateActivityRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateActivityRequest;

  static equals(a: CreateActivityRequest | PlainMessage<CreateActivityRequest> | undefined, b: CreateActivityRequest | PlainMessage<CreateActivityRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.ListActivityRequest
 */
export declare class ListActivityRequest extends Message<ListActivityRequest> {
  /**
   * 分页参数
   *
   * @generated from field: step.raccoon.common.Pagination pagination = 1;
   */
  pagination?: Pagination;

  /**
   * @generated from field: step.raccoon.creatoractivity.ListActivityParam search = 2;
   */
  search?: ListActivityParam;

  constructor(data?: PartialMessage<ListActivityRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.ListActivityRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListActivityRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListActivityRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListActivityRequest;

  static equals(a: ListActivityRequest | PlainMessage<ListActivityRequest> | undefined, b: ListActivityRequest | PlainMessage<ListActivityRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.ListActivityParam
 */
export declare class ListActivityParam extends Message<ListActivityParam> {
  /**
   * @generated from field: string activity_id = 1;
   */
  activityId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  constructor(data?: PartialMessage<ListActivityParam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.ListActivityParam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListActivityParam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListActivityParam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListActivityParam;

  static equals(a: ListActivityParam | PlainMessage<ListActivityParam> | undefined, b: ListActivityParam | PlainMessage<ListActivityParam> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.ListActivityResponse
 */
export declare class ListActivityResponse extends Message<ListActivityResponse> {
  /**
   * @generated from field: repeated step.raccoon.creatoractivity.AdminActivity data = 1;
   */
  data: AdminActivity[];

  /**
   * @generated from field: string next_cursor = 2;
   */
  nextCursor: string;

  constructor(data?: PartialMessage<ListActivityResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.ListActivityResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListActivityResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListActivityResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListActivityResponse;

  static equals(a: ListActivityResponse | PlainMessage<ListActivityResponse> | undefined, b: ListActivityResponse | PlainMessage<ListActivityResponse> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.UpdateActivityRequest
 */
export declare class UpdateActivityRequest extends Message<UpdateActivityRequest> {
  /**
   * @generated from field: string activity_id = 1;
   */
  activityId: string;

  /**
   * @generated from field: step.raccoon.creatoractivity.AdminActivity activity = 2;
   */
  activity?: AdminActivity;

  constructor(data?: PartialMessage<UpdateActivityRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.UpdateActivityRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateActivityRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateActivityRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateActivityRequest;

  static equals(a: UpdateActivityRequest | PlainMessage<UpdateActivityRequest> | undefined, b: UpdateActivityRequest | PlainMessage<UpdateActivityRequest> | undefined): boolean;
}

/**
 * @generated from message step.raccoon.creatoractivity.DeleteActivityRequest
 */
export declare class DeleteActivityRequest extends Message<DeleteActivityRequest> {
  /**
   * @generated from field: string activity_id = 1;
   */
  activityId: string;

  constructor(data?: PartialMessage<DeleteActivityRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "step.raccoon.creatoractivity.DeleteActivityRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteActivityRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteActivityRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteActivityRequest;

  static equals(a: DeleteActivityRequest | PlainMessage<DeleteActivityRequest> | undefined, b: DeleteActivityRequest | PlainMessage<DeleteActivityRequest> | undefined): boolean;
}

