// @generated by protoc-gen-connect-es v1.4.0 with parameter "target=js+dts"
// @generated from file raccoon/creatoractivity/activity_admin.proto (package step.raccoon.creatoractivity, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CreateActivityRequest, DeleteActivityRequest, DeliverRewardRequest, ListActivityRequest, ListActivityResponse, RewardListRequest, RewardListResp, UpdateActivityConfRequest, UpdateActivityRequest } from "./activity_admin_pb.js";
import { Empty, MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service step.raccoon.creatoractivity.Admin
 */
export declare const Admin: {
  readonly typeName: "step.raccoon.creatoractivity.Admin",
  readonly methods: {
    /**
     * @generated from rpc step.raccoon.creatoractivity.Admin.DeliverReward
     */
    readonly deliverReward: {
      readonly name: "DeliverReward",
      readonly I: typeof DeliverRewardRequest,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.creatoractivity.Admin.RewardList
     */
    readonly rewardList: {
      readonly name: "RewardList",
      readonly I: typeof RewardListRequest,
      readonly O: typeof RewardListResp,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.creatoractivity.Admin.UpdateActivityConf
     */
    readonly updateActivityConf: {
      readonly name: "UpdateActivityConf",
      readonly I: typeof UpdateActivityConfRequest,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.creatoractivity.Admin.CreateActivity
     */
    readonly createActivity: {
      readonly name: "CreateActivity",
      readonly I: typeof CreateActivityRequest,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.creatoractivity.Admin.UpdateActivity
     */
    readonly updateActivity: {
      readonly name: "UpdateActivity",
      readonly I: typeof UpdateActivityRequest,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.creatoractivity.Admin.ListActivity
     */
    readonly listActivity: {
      readonly name: "ListActivity",
      readonly I: typeof ListActivityRequest,
      readonly O: typeof ListActivityResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc step.raccoon.creatoractivity.Admin.DeleteActivity
     */
    readonly deleteActivity: {
      readonly name: "DeleteActivity",
      readonly I: typeof DeleteActivityRequest,
      readonly O: typeof Empty,
      readonly kind: MethodKind.Unary,
    },
  }
};

