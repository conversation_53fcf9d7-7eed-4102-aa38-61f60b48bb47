{"games": [{"name": "炖图", "businessLogic": ["app/make-photo/index.tsx", "src/store/makePhotoV2.ts", "src/components/makePhoto/constant.ts", "src/components/makePhoto/pannel/index.tsx", "src/components/makePhoto/pannel/ThinkingBtn.tsx", "src/components/makePhoto/pannel/Clip.tsx", "src/components/makePhoto/previewView/index.tsx", "src/components/makePhoto/loadingView/index.tsx", "src/components/makePhoto/bottomPannel/index.tsx", "src/components/makePhoto/bottomTab/index.tsx", "src/components/makePhoto/promptPannel/index.tsx", "src/components/makePhoto/promptCatalog/index.tsx", "src/components/makePhoto/promptList/index.tsx", "src/components/makePhoto/roleSelector/index.tsx", "src/components/makePhoto/styleModal/index.tsx", "src/components/makePhoto/utils/index.tsx", "src/api/makephoto/index.ts", "src/hooks/useTakePhoto.ts", "src/store/makePhotoEdit.ts", "src/components/popup/MakePhotoFixExceptionModal.tsx"]}, {"name": "动态LIVE", "businessLogic": ["app/live-photo-publish/index.tsx", "src/store/live.ts", "src/api/livephoto/index.ts", "src/bizComponents/livePhotoPublish/index.tsx", "src/bizComponents/livePhotoPublish/usePublishHandler.ts", "src/bizComponents/livePhotoPublish/usePlayHandler.ts", "src/bizComponents/livePhotoPublish/useEditHandler.ts", "src/bizComponents/livePhotoPublish/VideoHeader.tsx", "src/bizComponents/livePhotoPublish/BgmSelectPanel.tsx", "src/bizComponents/livePhotoPublish/PublishButton.tsx", "src/bizComponents/livePhotoPublish/PublishDetail.tsx", "src/bizComponents/livePhotoPublish/utils.ts", "src/bizComponents/livePhotoPublish/consts.ts", "src/bizComponents/livePhotoPublish/components/PhotosHeader.tsx", "src/bizComponents/livePhotoPublish/components/RegenBtn.tsx", "src/bizComponents/livePhotoPublish/components/specialEffectsPanel/index.tsx", "src/bizComponents/livePhotoPublish/components/specialEffectsPanel/SpecialEffectsTemplate.tsx", "src/bizComponents/livePhotoPublish/components/specialEffectsPanel/EffectList.tsx", "src/bizComponents/livePhotoPublish/bgmComponents/BgmView.tsx", "src/bizComponents/livePhotoPublish/bgmComponents/bgmList.tsx", "src/bizComponents/livePhotoPublish/bgmComponents/TopTabs.tsx", "src/bizComponents/livePhotoScreen/ImmersiveVideo.tsx", "src/bizComponents/livePhotoScreen/LoadingScreen.tsx", "src/bizComponents/livePhotoScreen/TakeSameSheet.tsx", "src/bizComponents/livePhotoScreen/actionsLayer/index.tsx", "src/bizComponents/livePhotoScreen/actionsLayer/LivePhotoActions.tsx", "src/bizComponents/livePhotoScreen/actionsLayer/LivePhotoDetails.tsx", "src/bizComponents/livePhotoScreen/actionsLayer/LivePhotoBottom.tsx", "src/bizComponents/livePhotoScreen/actionsLayer/VideoProgress.tsx", "src/bizComponents/livePhotoScreen/actionsLayer/SeekPreview.tsx", "src/bizComponents/livePhotoScreen/actionsLayer/Author.tsx", "src/bizComponents/livePhotoScreen/actionsLayer/AbsoluteIconBack.tsx", "src/bizComponents/livePhotoScreen/constants.ts", "src/bizComponents/liveScreen/index.tsx", "src/bizComponents/liveScreen/constants.ts", "src/bizComponents/PhotoEditor/components/templates/LiveTemplates.tsx"]}, {"name": "赛博谷子", "businessLogic": ["app/goods/index.tsx", "app/goods/home.tsx", "app/goods/edit.tsx", "src/store/goods.ts", "src/api/goods/index.ts", "src/bizComponents/goods/index.tsx", "src/bizComponents/goods/consts.ts", "src/bizComponents/goods/useGoodsCreateHandler.ts", "src/bizComponents/goods/BackGroundView.tsx", "src/bizComponents/goods/edit/index.tsx", "src/bizComponents/goods/edit/type.ts", "src/bizComponents/goods/edit/components/Cropper.tsx", "src/bizComponents/goods/edit/components/material/index.tsx", "src/bizComponents/goods/edit/components/material/consts.ts", "src/bizComponents/goods/generate/index.tsx", "src/bizComponents/goods/generate/Progress.tsx", "src/bizComponents/goodsHome/index.tsx", "src/bizComponents/goodsHome/types.tsx", "src/bizComponents/goodsHome/useGoodsHomeHandler.ts", "src/bizComponents/goodsHome/GoodsHomeShareModal.tsx", "src/bizComponents/goodsHome/components/GoodsList.tsx", "src/bizComponents/goodsHome/components/GoodsWall.tsx", "src/bizComponents/goodsHome/components/GoodsWallTemplates.tsx", "src/bizComponents/goodsHome/components/GoodsDetailModal.tsx", "src/bizComponents/goodsHome/components/GuestLike.tsx", "src/bizComponents/goodsHome/components/useListHeight.ts"]}, {"name": "平行世界", "businessLogic": ["app/parallel-world/[id].tsx", "app/parallel-world/center.tsx", "src/store/world/index.ts", "src/store/world/parallel-world.ts", "src/store/world/parallel-world-consumer.ts", "src/store/world/parallel-world-feed.ts", "src/store/world/parallel-world-publish.ts", "src/store/world/models/ActModel.ts", "src/store/world/models/PlotModel.ts", "src/store/world/models/WorldModel.ts", "src/api/parallel-world/index.ts", "src/api/parallel-world/consumer.ts", "src/api/parallel-world/feed.ts", "src/api/parallel-world/publish.ts", "src/bizComponents/parallelWorld/screens/main.tsx", "src/bizComponents/parallelWorld/screens/consumer.tsx", "src/bizComponents/parallelWorld/screens/feed.tsx", "src/bizComponents/parallelWorld/screens/publish.tsx", "src/bizComponents/parallelWorld/genCard/index.tsx", "src/bizComponents/parallelWorld/genCard/MaskCard.tsx", "src/bizComponents/parallelWorld/genCard/StaticCard.tsx", "src/bizComponents/parallelWorld/genCard/LoadingCard.tsx", "src/bizComponents/parallelWorld/genCard/StreamTextItem.tsx", "src/bizComponents/parallelWorld/genCard/CharacterCountdown.tsx", "src/bizComponents/parallelWorld/genCard/change-img-button.tsx", "src/bizComponents/parallelWorld/genCard/display-mask-card.hook.ts", "src/bizComponents/parallelWorld/sceneViewer/index.tsx", "src/bizComponents/parallelWorld/timeline/index.tsx", "src/bizComponents/parallelWorld/timeline/SectionCard.tsx", "src/bizComponents/parallelWorld/timeline/TLCard.tsx", "src/bizComponents/parallelWorld/bottomBar/index.tsx", "src/bizComponents/parallelWorld/nextChapterModal/index.tsx", "src/bizComponents/parallelWorld/textEditModal/index.tsx", "src/bizComponents/parallelWorld/feedInputModal/index.tsx", "src/bizComponents/parallelWorld/feedInputModal/hook.ts", "src/bizComponents/parallelWorld/choiceCard/index.tsx", "src/bizComponents/parallelWorld/choiceCard/ChoiceCard2.tsx", "src/bizComponents/parallelWorld/choiceCard/ChoiceInputCard.tsx", "src/bizComponents/parallelWorld/choiceCard/ai-choice-card.tsx", "src/bizComponents/parallelWorld/brandWorldList/index.tsx", "src/bizComponents/parallelWorld/constants.ts", "src/bizComponents/parallelWorld/header/index.tsx", "src/bizComponents/parallelWorld/header/useWorldShareCompConfig.ts", "src/bizComponents/parallelWorld/voiceModal/index.tsx", "src/bizComponents/parallelWorld/infoCard/index.tsx", "src/bizComponents/parallelWorld/others/LiHelp.tsx", "src/bizComponents/parallelWorld/others/ParallelWorldButton.tsx", "src/bizComponents/parallelWorld/others/radio.tsx", "src/bizComponents/parallelWorld/others/loading-img.tsx", "src/bizComponents/parallelWorld/others/linear-gradient-card.tsx", "src/bizComponents/parallelWorld/others/ai-gen-textarea.tsx", "src/bizComponents/parallelWorld/others/countdown.tsx", "src/bizComponents/parallelWorld/others/sentence-streamer.tsx", "src/bizComponents/parallelWorld/_components/consumer-tool-bar/index.tsx", "src/bizComponents/parallelWorld/_components/cover-change-modal/index.tsx", "src/bizComponents/parallelWorld/_hooks/stream-text.hook.ts", "src/bizComponents/parallelWorld/loading/countdown-loading.tsx", "src/bizComponents/parallelWorld/loading/plot-loading.tsx", "src/bizComponents/parallelWorld/topic/WorldTopicHeader.tsx", "src/bizComponents/parallelWorld/topic/WorldTopicInfo.tsx"]}, {"name": "飞天小女警/三只松鼠/<PERSON><PERSON>u", "businessLogic": ["app/ip/[brandId].tsx", "app/ai-play-next/index.tsx", "src/bizComponents/playgoundScreen/normalGame/index.tsx", "src/bizComponents/playgoundScreen/normalGame/useDataHandler.tsx", "src/bizComponents/playgoundScreen/normalGame/next/index.tsx", "src/bizComponents/playgoundScreen/normalGame/next/types.ts", "src/store/normalGame.tsx", "src/store/brand.ts", "src/api/magicflow/index.ts", "src/components/publishEntry/constant.ts", "src/components/publishEntry/useGameEntryConfig.tsx", "proto-registry/src/web/raccoon/common/types_pb.d.ts", "proto-registry/src/web/raccoon/magicflow/magicflow_common_pb.d.ts", "proto-registry/src/web/raccoon/magicflow/magicflow_pb.d.ts", "proto-registry/src/web/raccoon/magicflow/magicflow_connect.ts", "src/bizComponents/topicDetailScreen/TopicInfo.tsx", "src/bizComponents/topicDetailScreen/TopicDetailHeader.tsx", "src/bizComponents/topicScreen/TopicScene.tsx", "src/bizComponents/roleSelector/index.tsx", "src/components/makePhoto/roleSelector/IPRoleSelector.tsx", "src/bizComponents/role-create/EditRole/index.tsx", "src/bizComponents/role-create/editForm/index.tsx", "src/bizComponents/role-create/generateRole/intex.tsx", "src/store/role.ts", "src/store/roleHome.ts", "src/utils/formatRole.ts", "src/api/role/index.ts", "src/hooks/useRoleDetail.ts", "src/bizComponents/livePhotoPublish/usePublishHandler.ts", "src/bizComponents/livePhotoPublish/utils.ts", "src/bizComponents/livePhotoPublish/PublishButton.tsx"]}, {"name": "萌偶摇", "businessLogic": ["src/store/boomWen.ts", "src/api/boomWen/index.ts", "src/bizComponents/boomRoomScreen/index.tsx", "src/bizComponents/playgoundScreen/boomWen/index.tsx", "app/boom-room/index.tsx", "src/bizComponents/boomRoomScreen/GenerateImgScreen.tsx", "app/boom-room/generate-image.tsx", "src/bizComponents/boomRoomScreen/CustomLyricsScreen.tsx", "app/boom-room/custom-lyrics.tsx", "src/bizComponents/boomRoomScreen/usePlayHandler.ts", "src/bizComponents/boomRoomScreen/type.ts", "src/bizComponents/boomRoomScreen/constants.ts", "src/bizComponents/boomRoomScreen/components/imgActionSheet/index.tsx", "src/bizComponents/boomRoomScreen/components/imgActionSheet/useDataHandler.ts", "src/bizComponents/boomRoomScreen/components/musicList/index.tsx", "src/bizComponents/boomRoomScreen/components/musicItem/index.tsx", "src/bizComponents/boomRoomScreen/components/lyrics/index.tsx", "src/bizComponents/boomRoomScreen/components/msgListSheet/index.tsx", "src/bizComponents/boomRoomScreen/components/saveImgSheet/index.tsx", "proto-registry/src/web/raccoon/dance-together/dance_together_connect.d.ts", "proto-registry/src/web/raccoon/dance-together/dance_together_pb.d.ts"]}, {"name": "脑洞闯关", "businessLogic": ["app/decision/entry.tsx", "src/store/decision.ts", "src/api/decision/index.ts", "src/bizComponents/decision/decisionList/index.tsx", "src/bizComponents/decision/decisionList/decisionListCard.tsx", "src/bizComponents/decision/constants.ts", "src/bizComponents/decision/plot/index.ts", "src/bizComponents/decision/plot/types.ts", "src/bizComponents/decision/plot/meta.ts", "src/bizComponents/decision/plot/utils.ts", "app/decision-rank/[id].tsx", "src/bizComponents/decision/rank/index.tsx", "src/bizComponents/decision/rank/decisionRankGo.hook.ts"]}, {"name": "万物皆可挂件", "businessLogic": ["src/bizComponents/playgoundScreen/jellycat/index.tsx", "src/bizComponents/playgoundScreen/jellycat/useDataHandler.tsx", "src/bizComponents/playgoundScreen/jellycat/AnimatedJellycatScan.tsx", "src/store/jellycat.tsx", "src/api/jellycat/index.ts", "app/playground/index.tsx", "src/bizComponents/playgoundScreen/constants.ts", "src/components/publishEntry/constant.ts", "src/components/waterfall/type.ts"]}, {"name": "宅舞", "businessLogic": ["src/store/houseDance.ts", "src/api/otakudance/index.ts", "src/bizComponents/livePhotoPublish/usePublishHandler.ts", "app/house-dance/house-template.tsx", "src/bizComponents/houseTemScreen/index.tsx", "src/bizComponents/houseTemScreen/components/footer/index.tsx", "src/bizComponents/houseTemScreen/components/footer/RoleListModel.tsx", "src/bizComponents/houseTemScreen/components/footer/RoleItem.tsx", "src/bizComponents/houseTemScreen/components/templateList/index.tsx", "src/bizComponents/houseTemScreen/components/templateList/TemplateItem.tsx", "src/bizComponents/playgoundScreen/houseDance/index.tsx", "src/bizComponents/playgoundScreen/houseDance/components/PreviewHouseVideo/index.tsx", "src/bizComponents/playgoundScreen/houseDance/components/PreviewHouseVideo/VideoItem.tsx", "proto-registry/src/web/raccoon/otakudance/otakudance_connect.d.ts", "proto-registry/src/web/raccoon/otakudance/otakudance_pb.d.ts", "src/api/role/index.ts"]}, {"name": "灵魂提取器", "businessLogic": ["app/soul-maker/index.tsx", "src/store/soulmaker.ts"]}, {"name": "视频魔改", "businessLogic": ["app/magic-video-edit/index.tsx", "app/magic-video-publish/index.tsx", "src/store/video-magic/index.ts", "src/api/magicvideo/index.ts", "src/bizComponents/videoMagic/button/generateButton.tsx", "src/bizComponents/magic-video/publish/index.tsx", "app/magic-video-edit/clip.tsx", "app/magic-video-edit/script-video.tsx", "app/magic-video-edit/frame-picker.tsx", "src/store/video-magic/feed.ts", "src/bizComponents/magic-video/index.tsx", "src/bizComponents/magic-video/workCard/index.tsx", "src/bizComponents/magic-video/videoBox/index.tsx", "src/bizComponents/magic-video/draggieStoryboard/index.tsx", "src/bizComponents/magic-video/draggieStoryboard/boardItem.tsx", "src/bizComponents/magic-video/components/videoPreview/index.tsx", "src/bizComponents/magic-video/components/exampleList/index.tsx", "src/bizComponents/videoMagic/crop-editor/index.tsx", "src/bizComponents/videoMagic/ttsPanel/index.tsx", "src/bizComponents/videoMagic/galleryPanel/index.tsx", "src/bizComponents/videoMagic/framePicker/index.tsx", "src/bizComponents/videoMagic/frameSegPicker/index.tsx", "src/bizComponents/videoMagic/edit/videoWrapper.tsx", "src/bizComponents/videoMagic/edit/imageWrapper.tsx", "src/bizComponents/videoMagic/imageClip/index.tsx", "src/bizComponents/videoMagic/frameCover/index.tsx", "src/bizComponents/videoMagic/scriptInput/index.tsx", "src/bizComponents/videoMagic/progress/index.tsx", "src/bizComponents/magic-video/magicBox/index.tsx", "src/bizComponents/magic-video/scriptMask/index.tsx", "src/bizComponents/magic-video/imageBox/index.tsx", "src/bizComponents/magic-video/ttsBang/index.tsx", "src/bizComponents/magic-video/bgmPannelStatic/index.tsx", "src/bizComponents/magic-video/toast/index.tsx", "src/bizComponents/magic-video/type.ts", "src/bizComponents/magic-video/utils/index.ts", "src/bizComponents/videoMagic/ttsPanel/randomTTS.ts", "src/bizComponents/videoMagic/crop-editor/utils.tsx", "src/bizComponents/videoMagic/crop-editor/guide.tsx", "src/bizComponents/videoMagic/statusCode/index.tsx", "src/bizComponents/webviewScreen/hooks/webviewMagicVideo.hook.ts", "src/components/video/BaseVideo.tsx", "src/components/video/VideoControl.tsx", "src/components/video/index.tsx"]}, {"name": "梗图", "businessLogic": ["src/api/meme/index.ts", "src/store/meme.tsx", "app/meme/effect.tsx", "app/meme/role-select.tsx", "app/memePublish/index.tsx", "src/bizComponents/playgoundScreen/meme/index.tsx", "src/bizComponents/playgoundScreen/meme/MemePublish.tsx", "src/bizComponents/playgoundScreen/meme/MemeEdit.tsx", "src/bizComponents/playgoundScreen/meme/AnimatedMemeScan.tsx", "src/bizComponents/playgoundScreen/meme/AnimatedMemeMedium.tsx", "proto-registry/src/web/raccoon/meme/meme_connect.d.ts", "proto-registry/src/web/raccoon/meme/meme_pb.d.ts", "proto-registry/src/web/raccoon/meme/meme_internal_connect.d.ts", "proto-registry/src/web/raccoon/meme/meme_internal_pb.d.ts", "src/components/makePhoto/roleSelector/IPRoleSelector.tsx", "src/bizComponents/PhotoEditor/components/TextEditor.tsx", "src/bizComponents/PhotoEditor/components/TextPanel.tsx", "src/bizComponents/PhotoEditor/hooks/useTextEditor.tsx", "src/components/share/ShareComp.tsx", "src/components/share/ShareGenImage.tsx", "src/api/share/index.ts"]}, {"name": "梦境拍立得", "businessLogic": ["app/dream-mirror-polaroid/dream-desc.tsx", "app/dream-mirror-polaroid/dream-select.tsx", "src/store/dreamMirrorPolaroid.ts"]}, {"name": "PK", "businessLogic": ["app/pk/[id].tsx", "app/pk-nominate/index.tsx"]}, {"name": "BBS", "businessLogic": ["app/bbs-feed/index.tsx", "src/store/bbs.ts"]}, {"name": "表情包", "businessLogic": ["app/emoji/create.tsx", "app/emoji/preview.tsx", "src/store/emoji.ts"]}], "nativeCode": {"iOS": ["ios/lipu/AppDelegate.h", "ios/lipu/AppDelegate.mm", "ios/lipu/main.m", "ios/lipu/lipu-Bridging-Header.h", "ios/lipu/noop-file.swift", "ios/GTNotificationServiceExtension/NotificationService.swift", "ios/lipu/Info.plist", "native/ios/app/ServiceRegistry/Source/ServiceRegistry.swift", "node_modules/@step.ai/react-native-service/ios/ReactNativeService.swift", "node_modules/@step.ai/rangers-apm-plus/ios/RAPMConstants.h", "node_modules/@step.ai/rangers-apm-plus/ios/RAPMConstants.m", "node_modules/@step.ai/rangers-apm-plus/ios/RAPMPlusConfig.swift", "node_modules/@step.ai/rangers-apm-plus/ios/RAPMPlusServiceReg.swift", "node_modules/@step.ai/logging-module/ios/Service/NetLogConstant.h", "node_modules/@step.ai/logging-module/ios/Service/NetLogConstant.m", "node_modules/@step.ai/logging-module/ios/Service/LogServiceRegistry.swift", "node_modules/@step.ai/logging-module/ios/Module/LoggingModule.swift", "node_modules/@step.ai/logging-module/ios/Module/NetLogADSubscriber.swift", "node_modules/@step.ai/step-apm/ios/StepAPMADSub.swift", "node_modules/@step.ai/step-apm/ios/StepAPMInstaller.swift", "node_modules/@step.ai/photo-module/ios/Module/PhotoModule.swift", "node_modules/@step.ai/photo-module/ios/Registry/PhotoServiceRegistry.swift", "node_modules/@step.ai/photo-module/ios/Module/Proto/step_photo_v1_photo.pb.swift", "node_modules/@step.ai/payment-module/ios/Module/DTO.swift", "node_modules/@step.ai/payment-module/ios/Module/PaymentModule.swift", "node_modules/@step.ai/payment-module/ios/Registry/PaymentServiceRegistry.swift", "node_modules/@step.ai/comm-module/ios/CommModule.swift", "node_modules/@step.ai/rangers-applog/ios/TrackerServiceReg.swift", "node_modules/@step.ai/rangers-applog/ios/RAppLogConstants.h", "node_modules/@step.ai/rangers-applog/ios/RAppLogConstants.m", "node_modules/@step.ai/tos-module/ios/Module/TosModule.swift", "node_modules/@step.ai/tos-module/ios/Registry/ToSServiceRegistry.swift", "node_modules/@step.ai/tos-module/ios/Registry/ToSConst.h", "node_modules/@step.ai/tos-module/ios/Registry/ToSConst.m", "node_modules/@step.ai/navigator-module/ios/Module/NavigatorModule.swift", "node_modules/@step.ai/navigator-module/ios/Module/NavModuleADSubscriber.swift", "node_modules/@step.ai/navigator-module/ios/Registry/NavigatorServiceReg.swift", "node_modules/@step.ai/navigator-module/ios/Module/Protos/step_navigator_v1_navigation.pb.swift", "node_modules/@step.ai/devtools/ios/Module/DevItemView.swift", "node_modules/@step.ai/devtools/ios/Module/DevtoolsModule.swift", "node_modules/@step.ai/devtools/ios/Registry/DevToolsRegistry.swift", "node_modules/@step.ai/devtools/ios/Module/Exceptions.swift", "node_modules/@step.ai/devtools/ios/Module/Proto/step_dev_v1_item.pb.swift", "node_modules/@step.ai/api-module/ios/Source/APIModule.swift", "node_modules/@step.ai/api-module/ios/Source/Exceptions.swift", "node_modules/@step.ai/api-module/ios/Source/Protos/step_api_v1_oasis.pb.swift", "node_modules/@step.ai/share-module/ios/Module/Exceptions.swift", "node_modules/@step.ai/share-module/ios/Module/ShareModule.swift", "node_modules/@step.ai/share-module/ios/Registry/SocialServiceRegistry.swift", "node_modules/@step.ai/share-module/ios/Registry/SocialServiceConstants.h", "node_modules/@step.ai/share-module/ios/Registry/SocialServiceConstants.m", "node_modules/@step.ai/share-module/ios/Module/Protos/step_share_v1_share.pb.swift", "node_modules/@step.ai/app-info-module/ios/Module/ADSubscriber.swift", "node_modules/@step.ai/app-info-module/ios/Module/AppInfoModule.swift", "node_modules/@step.ai/app-info-module/ios/Registry/AppInfoRegistry.swift", "node_modules/@step.ai/app-info-module/ios/Registry/STPAppInfoRegOptions.h", "node_modules/@step.ai/app-info-module/ios/Registry/STPAppInfoRegOptions.m", "node_modules/@step.ai/publish-module/ios/Utils.swift", "node_modules/@step.ai/publish-module/ios/StepAIPublishServiceReg.swift", "node_modules/@step.ai/publish-module/ios/StepAIPublish.swift", "node_modules/@step.ai/number-auth/ios/Core/NumberAuthService.swift", "node_modules/@step.ai/number-auth/ios/Register/NumberAuthAppLifecycleDelegate.swift", "node_modules/@step.ai/number-auth/ios/AliYun/Src/AlYunNumberAuthService.swift", "node_modules/@step.ai/react-native-update/ios/RCTPushy/HDiffPatch/HDiffPatch.mm", "node_modules/@step.ai/react-native-update/ios/RCTPushy/HDiffPatch/HDiffPatch.h", "node_modules/@step.ai/apm-module/ios/Core/BootingProtect/StepAiAPMBootingProtect.h", "node_modules/@step.ai/apm-module/ios/Core/BootingProtect/StepAiAPMBootingProtect.m", "node_modules/@step.ai/apm-module/ios/Core/Log/StepAiAPMCXXLogHandler.h", "node_modules/@step.ai/apm-module/ios/Core/Memory/StepAiAPMHermesMemoryStatistics.h", "proto-registry/src/ios/message/passport/Sources/proto_api_inner_v1_service.pb.swift", "proto-registry/src/ios/message/passport/Sources/proto_api_passport_v1_global.pb.swift", "proto-registry/src/ios/message/passport/Sources/proto_risk_v1_risk.pb.swift", "proto-registry/src/ios/message/passport/Sources/proto_base_v1_base.pb.swift", "proto-registry/src/ios/message/passport/Sources/proto_error_v1_error.pb.swift", "proto-registry/src/ios/message/passport/Sources/proto_api_user_v1_service.pb.swift", "proto-registry/src/ios/message/passport/Sources/proto_api_passport_v1_service.pb.swift", "proto-registry/src/ios/message/passport/Sources/proto_resource_v1_resource.pb.swift", "proto-registry/src/ios/message/step-bridge/Sources/step_push_v1_push.pb.swift", "proto-registry/src/ios/rpc/passport/Sources/proto_api_passport_v1_global.connect.swift", "proto-registry/src/ios/rpc/passport/Sources/proto_api_user_v1_service.connect.swift", "proto-registry/src/ios/rpc/passport/Sources/proto_api_inner_v1_service.connect.swift", "proto-registry/src/ios/rpc/passport/Sources/proto_api_passport_v1_service.connect.swift"], "Android": ["android/app/src/main/java/com/lipuhome/app/MainActivity.kt", "android/app/src/main/java/com/lipuhome/app/MainApplication.kt", "android/app/build.gradle", "android/gradle/wrapper/gradle-wrapper.properties", "native/android/common/account/src/main/java/com/lipu/module/account/AccountService.kt", "native/android/common/account/src/main/java/com/lipu/module/account/AccountStorage.kt", "native/android/common/account/src/main/java/com/lipu/module/account/IAccount.kt", "native/android/common/init/src/main/java/com/lipu/module/init/ShareInitializer.kt", "native/android/common/navigator/src/main/java/com/lipuhome/app/navigator/AlinkActivity.kt", "native/android/common/navigator/src/main/java/com/lipuhome/app/navigator/AlinkActivityIntentHandler.kt", "native/android/common/navigator/src/main/java/com/lipuhome/app/navigator/util/ActivityUtil.kt", "native/android/common/navigator/src/main/java/com/lipuhome/app/navigator/util/IntentHandler.kt", "native/android/common/hook/src/main/java/com/lipu/module/hook/Constants.kt", "native/android/common/hook/src/main/java/com/lipu/module/hook/PrivicyHook.kt", "native/android/common/hook/src/main/java/com/lipu/module/hook/RNCatalystHook.kt", "native/android/common/hook/src/main/java/com/lipu/module/hook/RNGroupHook.kt", "native/android/common/hook/src/main/java/com/lipu/module/hook/RNInitialHook.kt", "native/android/common/init/src/main/java/com/lipu/module/init/ChannelHeaderInitializer.kt", "native/android/common/init/src/main/java/com/lipu/module/init/LoggerInitializer.kt", "native/android/common/init/src/main/java/com/lipu/module/init/StepActivationLogger.kt", "native/android/common/navigator/src/main/java/com/lipuhome/app/navigator/DeeplinkActivity.kt", "native/android/common/navigator/src/main/java/com/lipuhome/app/navigator/MainActivityIntentHandler.kt", "native/android/common/hook/src/main/java/com/lipu/module/hook/RNFontHook.kt", "native/android/common/init/src/debug/java/com/lipu/module/init/DevToolInitializer.kt", "native/android/common/navigator/src/main/java/com/lipuhome/app/navigator/DeeplinkActivityIntentHandler.kt", "native/android/common/push/src/main/java/com/lipu/module/push/LipuPuActivity.kt", "native/android/common/push/src/main/java/com/lipu/module/push/LipuPuService.kt", "native/android/common/push/src/main/java/com/lipu/module/push/PushInitializer.kt", "native/android/common/init/src/main/java/com/lipu/module/init/AppInitializer.kt", "native/android/common/init/src/main/java/com/lipu/module/init/EventLoggerInitializer.kt", "native/android/common/init/src/main/java/com/lipu/module/init/SimplePoolWrapper.kt", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/MediaType.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/PermissionResponse.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/PermissionResponseOrBuilder.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/PermissionStatus.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/Photo.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/PhotoOption.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/PhotoOptionOrBuilder.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/PhotoOrBuilder.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/PhotoProto.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/PhotoResult.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/PhotoResultOrBuilder.java", "node_modules/@step.ai/photo-module/android/src/main/java/com/step/photo/v1/VideoType.java", "node_modules/@step.ai/photo-module/android/src/main/java/expo/modules/photo/PhotoModule.kt"]}}